<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>鸿至编辑器演示网页</title>
  <script src="http://*************:8080/sdk.js"></script>
  <style>
    body {
      display: flex;
      height: 100vh;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    .left-container {
      flex: 3;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border: 1px solid #ddd;
      padding: 20px;
    }

    .right-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: normal;
      padding: 10px;
      /*flex: 1;  
          background-color: #e5e5e5;
          padding: 10px;
          align-items:normal;*/
    }

    .section {
      margin-bottom: 70px;
    }

    .static-text {
      background-color: #ddd;
      padding: 5px 10px;
      margin-bottom: 5px;
    }

    .button-group {
      display: flex;
      flex-wrap: wrap;
    }

    .button {
      /* padding: 5px 10px;
      background-color: #333;
      color: #fff;
      border: none;
      cursor: pointer;*/
      margin-right: 5px;
      margin-bottom: 5px;
      padding: 5px 10px;
      background-color: #333;
      color: #fff;
      border: none;
      cursor: pointer;
    }
  </style>
</head>

<body>

  <div class="left-container" id="editor"></div>
  <div class="right-container">
    <!-- 第一组 -->
    <div class="section">
      <div class="static-text">演示病历</div>
      <div class="button-group">
        <button class="button" onclick="openFile('入院')">入院记录</button>
        <button class="button" onclick="openFile('出院')">出院记录</button>
        <button class="button" onclick="openFile('转出')">转出记录记录</button>
        <button class="button" onclick="openFile('病案首页')">病案首页</button>
        <button class="button" onclick="openFile('自理评估单')">评估量表</button>
      </div>
    </div>
    <!-- 第二组 -->
    <div class="section">
      <div class="static-text">编辑器功能演示</div>
      <div class="button-group">
        <button class="button" onclick="switchCleanMode()">清洁模式切换</button>
        <button class="button" onclick="exportToHtml()">导出html</button>
        <button class="button" onclick="exportToPdf()">导出pdf</button>
        <input type="file" id="fileInput" accept=".apo,.hz">
        <button class="button" onclick="exportToPdfbackgroud()">导出pdf(模拟后端)</button>

        <button class="button" onclick="disableOuterCopy()">禁止外部拷贝</button>
        <button class="button" onclick="CopyWithoutFormat()">拷贝不带格式</button>
        <button class="button" onclick="openFile('入院')">结构化元素</button>
        <button class="button" onclick="openFile('绘图')">绘图</button>
        <button class="button" onclick="openFile('级联')">级联</button>
        <button class="button" onclick="openFile('跌倒评估单')">计算级联</button>
        <button class="button" onclick="openFile('医学表达式')">医学表达式</button>
      </div>
    </div>
    <!-- 第三组 -->
    <div class="section">
      <div class="static-text">编辑器业务演示</div>
      <div class="button-group">
        <button class="button">病历合并</button>
        <button class="button">打印</button>
        <button class="button">浏览模式切换</button>
        <button class="button">修订</button>
        <button class="button">批注</button>
      </div>
    </div>
  </div>
  <script>
    var Editor = EmrEditorSDK.Editor;
    const dom = document.getElementById('div-editor');

    var editorFunction = null;
    /*
    编辑器初始化
    */
    async function init(options) {
      const vm = await new EmrEditorSDK.Editor().init(options);
      // 接口集合
      const editor = await vm.getEditor();
      editorFunction = editor;
      editor.setMenuBarVisible(true);
      // 设置事件
      vm.setEvent({
        nsoStructClick: (name, type, position) => {
          console.log(name, type, position);
        }
      });
      return vm.getEditor();
    }

    //调用编辑器初始化,获取到编辑器的实例对象
    editorFunction = init({
      dom: window.document.querySelector('div'),
      src: 'http://*************:8080/',
      option: {
        bShowMenu: true,
        bShowToolbar: true,
        isTest: false,
        theme: {
          NewControl: {
            ShowRegionOperator: false,
          }
        }
      }
    });

    /*
    功能函数 -- 打开文档
    */
    function openFile(name) {
      getfilecontentFromServer(getIntValueFromString(name))
        .then(blob => {
          // 在这里处理blob对象 
          //const blob2 = new Blob([blob], { type: 'application/apollo-zstd' });
          // 这里你可以进一步处理这个Blob对象，比如编辑器直接打开  
          editorFunction.openDocumentWithStream(blob);
        })
        .catch(error => {
          console.error('Error fetching blob:', error);
        });
    }

    /*
    辅助函数
    从数据库中读取文件
    */
    function getfilecontentFromServer(id) {
      // 构造请求的URL  
      const url = `http://*************:8080/demo/getFileContent?id=${id}`;


      // 使用fetch API发送GET请求  
      return fetch(url)
        .then(response => {
          // 确保响应成功  
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          // 读取响应内容  
          return response.text();
        })
        .then(base64Data => {
          // 移除base64编码的前缀（如果有的话）  
          const base64Prefix = 'data:application/octet-stream;base64,';
          let rawData = base64Data;
          if (base64Data.startsWith(base64Prefix)) {
            rawData = base64Data.substring(base64Prefix.length);
          }

          // 将base64字符串转换为Uint8Array  
          const byteCharacters = atob(rawData);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);

          // 创建Blob对象  
          const blob = new Blob([byteArray], { type: 'application/apollo-zstd' });
          return blob;

        })
        .catch(error => {
          console.error('There has been a problem with your fetch operation:', error);
        });
    }

    /*
    辅助函数，浏览下下载
    */
    function downloadBlobAsFile(filename, blobPromise) {
      blobPromise.then((blob) => {
        // 创建一个临时的a标签  
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.setAttribute('download', filename); // 设置下载文件名  
        document.body.appendChild(link);
        link.click(); // 模拟点击  
        document.body.removeChild(link); // 然后移除  
        URL.revokeObjectURL(link.href); // 释放URL对象  
      }).catch((error) => {
        console.error('下载文件失败:', error);
      });
    }

    /*
    功能函数 -- 将当前编辑器打开的病历转换成html
    */
    function exportToHtml() {

      editorFunction.exportToOtherFormatWithStream(2).then(blob => {
        // 在这里处理返回的 Blob 数据 创建一个下载链接并模拟点击下载
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = 'file.html';
        link.click();
      }).catch(error => {
        // 如果出现错误，可以在这里处理错误
        console.error("convert html error:", error);
      });
    }

    /*
    功能函数 -- 将当前编辑器打开的文档转换成pdf
    通过2个步骤完成。
    1）调用接口转换为html
    2） 请求后台服务，将html转换为pdf
    */
    function exportToPdf() {
      editorFunction.exportToOtherFormatWithStream(2).then(blob => {
        // 在这里处理返回的 Blob 数据
      // 构造FormData对象，将HTML数据放入其中
      const formData = new FormData();
        formData.append('file', blob);

      // 发送POST请求到后台服务
        fetch('http://*************:8080/editorSvr/v1/printToPdf', {
        method: 'POST',
        body: formData
      })
        .then(response => response.blob()) // 将返回的数据转换为Blob
        .then(blobData => {
          // 创建一个Blob URL
          const blobUrl = URL.createObjectURL(blobData);

          // 创建一个<a>元素
          const link = document.createElement('a');
          link.href = blobUrl;
          link.download = 'converted_pdf.pdf'; // 设置下载文件的文件名

          // 模拟点击下载
          link.click();

          // 释放Blob URL
          URL.revokeObjectURL(blobUrl);
        })
        .catch(error => {
            console.error('Error in pdf-convert:', error);
        });
      }).catch(error => {
        // 如果出现错误，可以在这里处理错误
        console.error("convert pdf error:", error);
      });

    }

    /*
    功能函数 -- 模拟后台服务-转换pdf, 只需要hz文件的原始数据即可完成转换
    */
    function exportToPdfbackgroud() {
      //读取input框的文件
      var fileInput = document.getElementById('fileInput');

      // 确保有选择文件
      if (fileInput.files.length === 0) {
        alert('Please select a file.');
        return;
      }

      // 获取第一个文件
      var file = fileInput.files[0];

      // 创建一个 FileReader 对象
      var reader = new FileReader();

      // 当文件读取完成时
      reader.onload = function (event) {
        // 获取文件内容
        var fileContent = event.target.result;

        // 构造FormData对象，将HTML转服务需要的数据放入其中
        const formData = new FormData();
        formData.append('needWaterMark', 0);
        formData.append('cleanMode', 1);

        var blob = new Blob([fileContent], { type: 'application/apollo-zstd' });
        formData.append('file',blob);

        // 调用上述两个函数，并处理返回的 PDF 数据
        requestConvertToHtml(formData)
         .then(htmlData => requestPrintToPdf(htmlData))
          .then(pdfBlob => {

           // 可以创建一个下载链接并模拟点击下载
           const blobUrl = URL.createObjectURL(pdfBlob);
           const link = document.createElement('a');
           link.href = blobUrl;
           link.download = 'converted_pdf.pdf';
           link.click();
         })
         .catch(error => {
           // 如果出现错误，可以在这里处理错误
           console.error("Error occurred:", error);
         });
      }
      reader.readAsArrayBuffer(file);


    }

    /*
    辅助函数
    用于请求 html转换服务并获取返回的数据
    */
    function requestConvertToHtml(formData) {
      return fetch('http://*************:8080/editorSvr/v1/convertToHtml', {
        method: 'POST',
        body: formData
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to fetch HTML data');
          }
          return response.text(); // 返回HTML数据的文本内容
        });
    }

    /*
    辅助函数
    用于请求 pdf转换服务
    */
    // 定义一个函数，
    function requestPrintToPdf(htmlData) {
      // 构造请求参数
      const formData = new FormData();
      var blob = new Blob([htmlData], { type: 'text/html' });
      formData.append('file', blob);

      // 发送 POST 请求到 printToPdf 服务
      return fetch('http://*************:8080/editorSvr/v1/printToPdf', {
        method: 'POST',
        body: formData
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to print to PDF');
          }
          return response.blob(); // 返回 PDF 数据的 Blob
        });
    }

    /*
    功能函数 -- 切换清洁模式
    */
    function switchCleanMode() {
      editorFunction.browseTemplet(1);
    }

    /*
    功能函数 -- 禁止外部拷贝
    */
    function disableOuterCopy() {
      editorFunction.enableCopyFromExternal(false);//禁止外部拷贝
    }

    /*
    功能函数 -- 禁止外部拷贝 控制拷贝格式
    */
    function CopyWithoutFormat() {
      editorFunction.enableCopyFromExternal(true);//允许外部拷贝
      editorFunction.setClipboardFormat(false, false);//外部内部拷贝都不带格式，第二个控制内部
    }


    const stringToIntMap = {
      '入院': 1,
      '出院': 2,
      '医学表达式': 3,
      '病案首页': 4,
      '级联': 5,
      '绘图': 6,
      '自理评估单': 7,
      '跌倒评估单': 8,
      '转出': 9
    };

    // 字符串对照函数  
    function getIntValueFromString(str) {
      // 检查对照表中是否存在该字符串  
      if (stringToIntMap.hasOwnProperty(str)) {
        // 返回对应的整数值  
        return stringToIntMap[str];
      } else {
        // 如果不存在，返回默认值（比如 -1）  
        return -1;
      }
    }  
  </script>

</body>

</html>