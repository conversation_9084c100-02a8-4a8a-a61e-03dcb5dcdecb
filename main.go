package main

import (
	"archive/zip"
	"bytes"
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/x509"
	_ "embed"
	"encoding/base32"
	"encoding/json"
	"encoding/pem"
	"errors"
	"flag"
	"fmt"
	"io"
	"io/fs"
	"log"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	rand2 "math/rand"

	"github.com/golang-jwt/jwt/v5"
	"github.com/julienschmidt/httprouter"
)

//go:embed static.zip
var staticFile []byte

//go:embed helper.zip
var helperFile []byte

//go:embed demov2.zip
var demov2 []byte

//go:embed publickey.pem
var pubkeyData []byte

var (
	hostWhiteList    []*regexp.Regexp
	refererWhiteList []*regexp.Regexp
	displayName      string
	expTime          int64
	port             int
	licence          string
	useTls           bool
	tlsCertFile      string
	tlsKeyFile       string
	versionNow       = 101 // 定义当前go程序的版本号 对应的licence.json里为授权允许的最高版本号
)

type StringQueue struct {
	queue map[string]bool
}

func NewStringQueue() *StringQueue {
	return &StringQueue{
		queue: make(map[string]bool),
	}
}

func (sq *StringQueue) Contains(s string) bool {
	_, exists := sq.queue[s]
	return exists
}

func (sq *StringQueue) AddIfNotExists(s string) {
	if !sq.Contains(s) {
		sq.queue[s] = true
	}
}

func init() {
	mime.AddExtensionType(".ttf", "application/ttf")
	mime.AddExtensionType(".woff", "application/woff")
	mime.AddExtensionType(".woff2", "application/woff2")
	mime.AddExtensionType(".db", "application/x-sqlite3")
}

func main() {
	flag.IntVar(&port, "port", 7075, "")
	flag.StringVar(&licence, "licence", "", "")
	privKeyFile := flag.String("privkey", "./privatekey.pem", "")
	jsonFile := flag.String("json", "./licence.json", "")
	genlis := flag.Bool("genlicence", false, "")
	genkey := flag.Bool("genkey", false, "")
	flag.BoolVar(&useTls, "tls", false, "")
	flag.StringVar(&tlsCertFile, "tlsCert", "server.crt", "https cert file")
	flag.StringVar(&tlsKeyFile, "tlsKey", "key.pem", "https private key file")
	flag.Parse()

	if *genkey {
		generateKey()
		os.Exit(0)
	} else if *genlis {
		content, err := os.ReadFile(*jsonFile)
		if err != nil {
			log.Fatalf("Failed to read json file: %v", err)
		}
		privKeyData, err := os.ReadFile(*privKeyFile)
		if err != nil {
			log.Fatalf("Failed to read private key file: %v", err)
		}
		generateLicence(privKeyData, content)
		os.Exit(0)
	} else {
		if len(licence) == 0 {
			licence = os.Getenv("EDITOR_LICENCE")
		}
		startServer()
	}
}

func generateKey() {
	privKey, err := ecdsa.GenerateKey(elliptic.P256(), rand.Reader)
	if err != nil {
		log.Fatalf("Failed to generate key: %v", err)
	}
	x509privkey, err := x509.MarshalPKCS8PrivateKey(privKey)
	if err != nil {
		log.Fatalf("Failed to marshal private key: %v", err)
	}
	privkeyBlock := &pem.Block{
		Type:  "PRIVATE KEY",
		Bytes: x509privkey,
	}
	x509pubkey, err := x509.MarshalPKIXPublicKey(&privKey.PublicKey)
	if err != nil {
		log.Fatalf("Failed to marshal public key: %v", err)
	}
	publicBlock := &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: x509pubkey,
	}
	err = os.WriteFile("privatekey.pem", pem.EncodeToMemory(privkeyBlock), 0666)
	if err != nil {
		log.Fatalf("Failed to write private key file: %v", err)
	}
	publicData := pem.EncodeToMemory(publicBlock)
	publicData = []byte(base32.StdEncoding.EncodeToString(publicData))
	rnd := rand2.New(rand2.NewSource(32768))
	for i := range publicData {
		publicData[i] = publicData[i] + byte(rnd.Int())
	}
	err = os.WriteFile("publickey.pem", publicData, 0666)
	if err != nil {
		log.Fatalf("Failed to write public key file: %v", err)
	}
}

func generateLicence(privKeyData, jsonContent []byte) {
	block, _ := pem.Decode(privKeyData)
	x509EncodedPriv := block.Bytes
	privKey, err := x509.ParsePKCS8PrivateKey(x509EncodedPriv)
	if err != nil {
		log.Fatalf("Failed to parse private key: %v", err)
	}
	content := make(map[string]interface{})
	err = json.Unmarshal(jsonContent, &content)
	if err != nil {
		log.Fatalf("Failed to unmarshal json content: %v", err)
	}
	claims := jwt.MapClaims{}
	for k, v := range content {
		claims[k] = v
	}
	claims["iss"] = "hongzhi"
	claims["iat"] = time.Now().Unix()
	token := jwt.NewWithClaims(jwt.SigningMethodES256, claims)
	ret, err := token.SignedString(privKey)
	if err != nil {
		log.Fatalf("Failed to sign token: %v", err)
	}
	fmt.Println(ret)
}

func startServer() {
	pubkeyDataDec := make([]byte, len(pubkeyData))
	rnd := rand2.New(rand2.NewSource(32768))
	for i := range pubkeyData {
		pubkeyDataDec[i] = pubkeyData[i] - byte(rnd.Int())
	}
	pubKeyData := make([]byte, base32.StdEncoding.DecodedLen(len(pubkeyDataDec)))
	base32.StdEncoding.Decode(pubKeyData, pubkeyDataDec)
	block, _ := pem.Decode(pubKeyData)
	x509EncodedPub := block.Bytes
	genericPubKey, err := x509.ParsePKIXPublicKey(x509EncodedPub)
	if err != nil {
		log.Fatalf("Failed to parse public key: %v", err)
	}
	pubkey := genericPubKey.(*ecdsa.PublicKey)
	token := licence
	jwtoken, err := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		if token.Method != jwt.SigningMethodES256 {
			return nil, errors.New("not valid alg")
		}
		return pubkey, nil
	})
	if err != nil {
		log.Fatalf("Failed to parse token: %v", err)
	}
	if !jwtoken.Valid {
		log.Fatalf("Invalid token")
	}
	claims := jwtoken.Claims.(jwt.MapClaims)
	if !jwtoken.Valid {
		log.Fatalf("Token validation failed: %v", err)
	}
	processClaims(claims)

	r := httprouter.New()

	init_Database()    //如果需要数据库
	initChromedpPool() // 如果需要启动转换服务 则需要先初始化
	start_gateway(r)   // 业务路由分发

	defer closeChromedpPool()
	defer close_Database()

	r.GET("/editor/mark", func(writer http.ResponseWriter, request *http.Request, params httprouter.Params) {

		// Handle preflight OPTIONS request
		if request.Method == "OPTIONS" {
			writer.WriteHeader(http.StatusOK)
			return
		}

		if request.URL.Query().Has("showLicence") {
			b, _ := json.Marshal(claims)
			writer.Write(b)
			return
		}

		uuid, err := decRequest([]byte(request.URL.RawQuery))
		if err != nil {
			log.Println(err)
			http.Error(writer, "bad request", http.StatusBadRequest)
			return
		}
		out := encResponse(0, uuid, displayName)
		writer.Write(out)
	})

	setupStaticFileServer(r)
	startHTTPServer(r)
}

func processClaims(claims jwt.MapClaims) {
	hosts := claims["aud"]
	if hosts == nil {
		log.Fatal("Missing required field: aud")
	}
	for _, h := range strings.Split(hosts.(string), ",") {
		e, err := regexp.Compile(strings.Replace(h, "*", ".*", -1))
		if err != nil {
			log.Fatalf("Failed to compile aud: %v", err)
		}
		hostWhiteList = append(hostWhiteList, e)
	}
	hosts = claims["ref"]
	if hosts == nil {
		log.Fatal("Missing required field: ref")
	}
	for _, h := range strings.Split(hosts.(string), ",") {
		e, err := regexp.Compile(strings.Replace(h, "*", ".*", -1))
		if err != nil {
			log.Fatalf("Failed to compile ref: %v", err)
		}
		refererWhiteList = append(refererWhiteList, e)
	}
	displayName = claims["dis"].(string)
	if displayName == "" {
		log.Fatal("Missing required field: dis")
	}
	expValue := claims["exp_date"]
	if expValue != nil {
		expTime = int64(expValue.(float64))
		currentTime := time.Now().UnixMilli()
		if currentTime > expTime {
			log.Fatal("Licence expired")
		}
	}
	expversion := claims["exp_version"]
	if expversion != nil {
		versionLicence := int(expversion.(float64))
		if versionNow > versionLicence {
			log.Fatalf("Version %d not supported", versionNow)
		}
	}
}

// 编辑器静态文件服务处理函数
func serveStaticFile(w http.ResponseWriter, r *http.Request, zipFS *zip.Reader) {
	// 获取请求的文件路径
	path := strings.TrimPrefix(r.URL.Path, "/")
	if path == "" {
		path = "index.html"
	}

	// 检查Accept-Encoding头是否支持gzip
	acceptEncoding := r.Header.Get("Accept-Encoding")
	supportsGzip := strings.Contains(acceptEncoding, "gzip")

	// 对于js文件，如果支持gzip，先尝试找.gz版本
	var f *zip.File
	if supportsGzip && strings.HasSuffix(path, ".js") {
		// 先查找.gz版本
		gzPath := path + ".gz"
		for _, zf := range zipFS.File {
			if zf.Name == "staticfiles/"+gzPath {
				f = zf
				w.Header().Set("Content-Encoding", "gzip")
				break
			}
		}
	}

	// 如果没有找到gz版本或不支持gzip，使用原始文件
	if f == nil {
		for _, zf := range zipFS.File {
			if zf.Name == "staticfiles/"+path {
				f = zf
				break
			}
		}
	}

	if f == nil {
		http.Error(w, "File not found", http.StatusNotFound)
		return
	}

	// 设置Content-Type
	ext := filepath.Ext(path)
	var mimeType string
	switch ext {
	case ".js":
		mimeType = "application/javascript"
	case ".css":
		mimeType = "text/css"
	default:
		mimeType = mime.TypeByExtension(ext)
		if mimeType == "" {
			mimeType = "application/octet-stream"
		}
	}
	w.Header().Set("Content-Type", mimeType)

	// 打开并读取文件内容
	rc, err := f.Open()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	defer rc.Close()

	_, err = io.Copy(w, rc)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
}

// 帮助文件：静态文件服务处理函数
func serveStaticFileHelper(w http.ResponseWriter, r *http.Request, filename string) {
	data, err := readZipFile(helperFile, filename)
	if err != nil {
		http.Error(w, err.Error(), http.StatusNotFound)
		return
	}

	ext := filepath.Ext(filename)
	var mimeType string
	switch ext {
	case ".js":
		mimeType = "application/javascript"
	case ".css":
		mimeType = "text/css"
	default:
		mimeType = mime.TypeByExtension(ext)
		if mimeType == "" {
			mimeType = "application/octet-stream"
		}
	}
	w.Header().Set("Content-Type", mimeType)

	_, err = w.Write(data)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
}

// 演示系统v2：静态文件服务处理函数
func serveStaticFileDemoV2(w http.ResponseWriter, r *http.Request, filename string) {
	data, err := readZipFile(demov2, filename)
	if err != nil {
		http.Error(w, err.Error(), http.StatusNotFound)
		return
	}

	ext := filepath.Ext(filename)
	var mimeType string
	switch ext {
	case ".js":
		mimeType = "application/javascript"
	case ".css":
		mimeType = "text/css"
	default:
		mimeType = mime.TypeByExtension(ext)
		if mimeType == "" {
			mimeType = "application/octet-stream"
		}
	}
	w.Header().Set("Content-Type", mimeType)

	_, err = w.Write(data)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
}

func setupStaticFileServer(r *httprouter.Router) {
	staticFileReader := bytes.NewReader(staticFile)
	zipFileFS, err := zip.NewReader(staticFileReader, int64(len(staticFile)))
	if err != nil {
		log.Fatalf("Failed to read static zip file: %v", err)
	}

	r.GET("/helper/*filepath", func(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
		filename := strings.TrimPrefix(r.URL.Path, "/helper/")
		serveStaticFileHelper(w, r, filename)
	})

	r.GET("/example/*filepath", func(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
		filename := strings.TrimPrefix(r.URL.Path, "/example/")
		serveStaticFileDemoV2(w, r, filename)
	})

	// NotFound 路由处理
	r.NotFound = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证 host 白名单
		valid := false
		host := r.Host
		for _, reg := range hostWhiteList {
			if valid = valid || reg.MatchString(host); valid {
				break
			}
		}
		if !valid {
			http.Error(w, "host not found", http.StatusNotFound)
			return
		}

		// 验证 referer 白名单
		referer := r.Header.Get("referer")
		valid = false
		for _, reg := range refererWhiteList {
			if valid = valid || reg.MatchString(referer); valid {
				break
			}
		}
		for _, reg := range hostWhiteList {
			if valid = valid || reg.MatchString(referer); valid {
				break
			}
		}
		if !valid {
			http.Error(w, "referer not allowed", http.StatusForbidden)
			return
		}

		// 使用自定义的静态文件处理函数
		serveStaticFile(w, r, zipFileFS)
	})
}

func startHTTPServer(r *httprouter.Router) {
	fmt.Printf("HzEditor Server is started, Listening on port %d\n", port)
	if useTls {
		log.Fatalln(http.ListenAndServeTLS(fmt.Sprintf("0.0.0.0:%d", port), tlsCertFile, tlsKeyFile, r))
	} else {
		log.Fatalln(http.ListenAndServe(fmt.Sprintf("0.0.0.0:%d", port), r))
	}
}

func readZipFile(zipData []byte, name string) ([]byte, error) {
	reader, err := zip.NewReader(bytes.NewReader(zipData), int64(len(zipData)))
	if err != nil {
		return nil, err
	}

	for _, f := range reader.File {
		if f.Name == name {
			rc, err := f.Open()
			if err != nil {
				return nil, err
			}
			defer rc.Close()

			data, err := io.ReadAll(rc)
			if err != nil {
				return nil, err
			}
			return data, nil
		}
	}

	return nil, fs.ErrNotExist
}
