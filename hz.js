const fs = require('fs-extra');
const archiver = require('archiver');
const path = require('path');
const unzipper = require('unzipper');

// Define paths
const staticZipFilePath = path.join(__dirname, 'static.zip');
const helperZipFilePath = path.join(__dirname, 'helper.zip');

const filesToAddToStaticZip = [
    { src: path.join(__dirname, 'demo3.html'), dest: 'staticfiles/demo3.html' },
    { src: path.join(__dirname, 'demo2.html'), dest: 'staticfiles/demo2.html' },
    { src: path.join(__dirname, 'demo.html'), dest: 'staticfiles/demo.html' },
    {src: path.join(__dirname, 'emr.json'), dest: 'staticfiles/emr.json' }
];

const filesToAddToHelperZip = [
    { src: path.join(__dirname, 'intro2.html'), dest: 'intro2.html' },
    { src: path.join(__dirname, 'intro.html'), dest: 'intro.html' }
];

// Function to add files to an existing ZIP file
async function addFilesToZip(zipPath, files) {
    const tempDir = path.join(__dirname, 'temp');
    
    // Ensure the temp directory exists
    await fs.ensureDir(tempDir);

    // Unzip the existing ZIP file into the temp directory
    await fs.createReadStream(zipPath)
        .pipe(unzipper.Extract({ path: tempDir }))
        .promise();

    // Create a new ZIP file including the old contents and new files
    const output = fs.createWriteStream(zipPath);
    const archive = archiver('zip', { zlib: { level: 9 } });

    return new Promise((resolve, reject) => {
        output.on('close', () => {
            console.log(`Updated ${archive.pointer()} total bytes in ${zipPath}`);
            fs.remove(tempDir).then(resolve).catch(reject);
        });

        archive.on('error', (err) => {
            reject(err);
        });

        archive.pipe(output);

        // Add existing contents of the ZIP file
        archive.directory(tempDir, false);

        // Add new files to the correct directory in the ZIP
        files.forEach(file => {
            // Ensure that we preserve the directory structure
            archive.file(file.src, { name: file.dest });
        });

        archive.finalize();
    });
}

// Main function to perform all tasks
async function main() {
    try {
        // Add files to static.zip
        await addFilesToZip(staticZipFilePath, filesToAddToStaticZip);

        // Add files to helper.zip
        await addFilesToZip(helperZipFilePath, filesToAddToHelperZip);
    } catch (err) {
        console.error('Error in main function:', err);
    }
}

main();
