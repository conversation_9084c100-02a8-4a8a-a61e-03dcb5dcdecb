!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports["hzEditor-Server"]=t():e["hzEditor-Server"]=t()}(global,(()=>(()=>{var e={5622:(e,t,r)=>{function n(e){return Object.prototype.toString.call(e)}t.isArray=function(e){return Array.isArray?Array.isArray(e):"[object Array]"===n(e)},t.isBoolean=function(e){return"boolean"==typeof e},t.isNull=function(e){return null===e},t.isNullOrUndefined=function(e){return null==e},t.isNumber=function(e){return"number"==typeof e},t.isString=function(e){return"string"==typeof e},t.isSymbol=function(e){return"symbol"==typeof e},t.isUndefined=function(e){return void 0===e},t.isRegExp=function(e){return"[object RegExp]"===n(e)},t.isObject=function(e){return"object"==typeof e&&null!==e},t.isDate=function(e){return"[object Date]"===n(e)},t.isError=function(e){return"[object Error]"===n(e)||e instanceof Error},t.isFunction=function(e){return"function"==typeof e},t.isPrimitive=function(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e},t.isBuffer=r(181).Buffer.isBuffer},874:e=>{"use strict";var t,r,n=global.MutationObserver||global.WebKitMutationObserver;if(process.browser)if(n){var i=0,a=new n(h),s=global.document.createTextNode("");a.observe(s,{characterData:!0}),t=function(){s.data=i=++i%2}}else if(global.setImmediate||void 0===global.MessageChannel)t="document"in global&&"onreadystatechange"in global.document.createElement("script")?function(){var e=global.document.createElement("script");e.onreadystatechange=function(){h(),e.onreadystatechange=null,e.parentNode.removeChild(e),e=null},global.document.documentElement.appendChild(e)}:function(){setTimeout(h,0)};else{var o=new global.MessageChannel;o.port1.onmessage=h,t=function(){o.port2.postMessage(0)}}else t=function(){process.nextTick(h)};var l=[];function h(){var e,t;r=!0;for(var n=l.length;n;){for(t=l,l=[],e=-1;++e<n;)t[e]();n=l.length}r=!1}e.exports=function(e){1!==l.push(e)||r||t()}},2017:(e,t,r)=>{try{var n=r(9023);if("function"!=typeof n.inherits)throw"";e.exports=n.inherits}catch(t){e.exports=r(6698)}},6698:e=>{"function"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}},4634:e=>{var t={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==t.call(e)}},2678:(e,t,r)=>{"use strict";var n=r(1132),i=r(6954),a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";t.encode=function(e){for(var t,r,i,s,o,l,h,c=[],u=0,d=e.length,f=d,p="string"!==n.getTypeOf(e);u<e.length;)f=d-u,p?(t=e[u++],r=u<d?e[u++]:0,i=u<d?e[u++]:0):(t=e.charCodeAt(u++),r=u<d?e.charCodeAt(u++):0,i=u<d?e.charCodeAt(u++):0),s=t>>2,o=(3&t)<<4|r>>4,l=f>1?(15&r)<<2|i>>6:64,h=f>2?63&i:64,c.push(a.charAt(s)+a.charAt(o)+a.charAt(l)+a.charAt(h));return c.join("")},t.decode=function(e){var t,r,n,s,o,l,h=0,c=0;if("data:"===e.substr(0,5))throw new Error("Invalid base64 input, it looks like a data url.");var u,d=3*(e=e.replace(/[^A-Za-z0-9+/=]/g,"")).length/4;if(e.charAt(e.length-1)===a.charAt(64)&&d--,e.charAt(e.length-2)===a.charAt(64)&&d--,d%1!=0)throw new Error("Invalid base64 input, bad content length.");for(u=i.uint8array?new Uint8Array(0|d):new Array(0|d);h<e.length;)t=a.indexOf(e.charAt(h++))<<2|(s=a.indexOf(e.charAt(h++)))>>4,r=(15&s)<<4|(o=a.indexOf(e.charAt(h++)))>>2,n=(3&o)<<6|(l=a.indexOf(e.charAt(h++))),u[c++]=t,64!==o&&(u[c++]=r),64!==l&&(u[c++]=n);return u}},8807:(e,t,r)=>{"use strict";var n=r(7882),i=r(4982),a=r(1919),s=r(8432);function o(e,t,r,n,i){this.compressedSize=e,this.uncompressedSize=t,this.crc32=r,this.compression=n,this.compressedContent=i}o.prototype={getContentWorker:function(){var e=new i(n.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new s("data_length")),t=this;return e.on("end",(function(){if(this.streamInfo.data_length!==t.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")})),e},getCompressedWorker:function(){return new i(n.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},o.createWorkerFrom=function(e,t,r){return e.pipe(new a).pipe(new s("uncompressedSize")).pipe(t.compressWorker(r)).pipe(new s("compressedSize")).withStreamInfo("compression",t)},e.exports=o},3078:(e,t,r)=>{"use strict";var n=r(193);t.STORE={magic:"\0\0",compressWorker:function(){return new n("STORE compression")},uncompressWorker:function(){return new n("STORE decompression")}},t.DEFLATE=r(2039)},8786:(e,t,r)=>{"use strict";var n=r(1132),i=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();e.exports=function(e,t){return void 0!==e&&e.length?"string"!==n.getTypeOf(e)?function(e,t,r,n){var a=i,s=0+r;e=~e;for(var o=0;o<s;o++)e=e>>>8^a[255&(e^t[o])];return~e}(0|t,e,e.length):function(e,t,r,n){var a=i,s=0+r;e=~e;for(var o=0;o<s;o++)e=e>>>8^a[255&(e^t.charCodeAt(o))];return~e}(0|t,e,e.length):0}},5051:(e,t)=>{"use strict";t.base64=!1,t.binary=!1,t.dir=!1,t.createFolders=!0,t.date=null,t.compression=null,t.compressionOptions=null,t.comment=null,t.unixPermissions=null,t.dosPermissions=null},7882:(e,t,r)=>{"use strict";var n;n="undefined"!=typeof Promise?Promise:r(9977),e.exports={Promise:n}},2039:(e,t,r)=>{"use strict";var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,i=r(1668),a=r(1132),s=r(193),o=n?"uint8array":"array";function l(e,t){s.call(this,"FlateWorker/"+e),this._pako=null,this._pakoAction=e,this._pakoOptions=t,this.meta={}}t.magic="\b\0",a.inherits(l,s),l.prototype.processChunk=function(e){this.meta=e.meta,null===this._pako&&this._createPako(),this._pako.push(a.transformTo(o,e.data),!1)},l.prototype.flush=function(){s.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},l.prototype.cleanUp=function(){s.prototype.cleanUp.call(this),this._pako=null},l.prototype._createPako=function(){this._pako=new i[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var e=this;this._pako.onData=function(t){e.push({data:t,meta:e.meta})}},t.compressWorker=function(e){return new l("Deflate",e)},t.uncompressWorker=function(){return new l("Inflate",{})}},3890:(e,t,r)=>{"use strict";var n=r(1132),i=r(193),a=r(8222),s=r(8786),o=r(6407),l=function(e,t){var r,n="";for(r=0;r<t;r++)n+=String.fromCharCode(255&e),e>>>=8;return n},h=function(e,t,r,i,h,c){var u,d,f=e.file,p=e.compression,m=c!==a.utf8encode,g=n.transformTo("string",c(f.name)),b=n.transformTo("string",a.utf8encode(f.name)),_=f.comment,y=n.transformTo("string",c(_)),w=n.transformTo("string",a.utf8encode(_)),v=b.length!==f.name.length,k=w.length!==_.length,x="",S="",C="",E=f.dir,A=f.date,T={crc32:0,compressedSize:0,uncompressedSize:0};t&&!r||(T.crc32=e.crc32,T.compressedSize=e.compressedSize,T.uncompressedSize=e.uncompressedSize);var B=0;t&&(B|=8),m||!v&&!k||(B|=2048);var N,R,O=0,z=0;E&&(O|=16),"UNIX"===h?(z=798,O|=(R=N=f.unixPermissions,N||(R=E?16893:33204),(65535&R)<<16)):(z=20,O|=63&(f.dosPermissions||0)),u=A.getUTCHours(),u<<=6,u|=A.getUTCMinutes(),u<<=5,u|=A.getUTCSeconds()/2,d=A.getUTCFullYear()-1980,d<<=4,d|=A.getUTCMonth()+1,d<<=5,d|=A.getUTCDate(),v&&(S=l(1,1)+l(s(g),4)+b,x+="up"+l(S.length,2)+S),k&&(C=l(1,1)+l(s(y),4)+w,x+="uc"+l(C.length,2)+C);var I="";return I+="\n\0",I+=l(B,2),I+=p.magic,I+=l(u,2),I+=l(d,2),I+=l(T.crc32,4),I+=l(T.compressedSize,4),I+=l(T.uncompressedSize,4),I+=l(g.length,2),I+=l(x.length,2),{fileRecord:o.LOCAL_FILE_HEADER+I+g+x,dirRecord:o.CENTRAL_FILE_HEADER+l(z,2)+I+l(y.length,2)+"\0\0\0\0"+l(O,4)+l(i,4)+g+x+y}},c=function(e){return o.DATA_DESCRIPTOR+l(e.crc32,4)+l(e.compressedSize,4)+l(e.uncompressedSize,4)};function u(e,t,r,n){i.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=t,this.zipPlatform=r,this.encodeFileName=n,this.streamFiles=e,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}n.inherits(u,i),u.prototype.push=function(e){var t=e.meta.percent||0,r=this.entriesCount,n=this._sources.length;this.accumulate?this.contentBuffer.push(e):(this.bytesWritten+=e.data.length,i.prototype.push.call(this,{data:e.data,meta:{currentFile:this.currentFile,percent:r?(t+100*(r-n-1))/r:100}}))},u.prototype.openedSource=function(e){this.currentSourceOffset=this.bytesWritten,this.currentFile=e.file.name;var t=this.streamFiles&&!e.file.dir;if(t){var r=h(e,t,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:r.fileRecord,meta:{percent:0}})}else this.accumulate=!0},u.prototype.closedSource=function(e){this.accumulate=!1;var t=this.streamFiles&&!e.file.dir,r=h(e,t,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(r.dirRecord),t)this.push({data:c(e),meta:{percent:100}});else for(this.push({data:r.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},u.prototype.flush=function(){for(var e=this.bytesWritten,t=0;t<this.dirRecords.length;t++)this.push({data:this.dirRecords[t],meta:{percent:100}});var r=this.bytesWritten-e,i=function(e,t,r,i,a){var s=n.transformTo("string",a(i));return o.CENTRAL_DIRECTORY_END+"\0\0\0\0"+l(e,2)+l(e,2)+l(t,4)+l(r,4)+l(s.length,2)+s}(this.dirRecords.length,r,e,this.zipComment,this.encodeFileName);this.push({data:i,meta:{percent:100}})},u.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},u.prototype.registerPrevious=function(e){this._sources.push(e);var t=this;return e.on("data",(function(e){t.processChunk(e)})),e.on("end",(function(){t.closedSource(t.previous.streamInfo),t._sources.length?t.prepareNextSource():t.end()})),e.on("error",(function(e){t.error(e)})),this},u.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},u.prototype.error=function(e){var t=this._sources;if(!i.prototype.error.call(this,e))return!1;for(var r=0;r<t.length;r++)try{t[r].error(e)}catch(e){}return!0},u.prototype.lock=function(){i.prototype.lock.call(this);for(var e=this._sources,t=0;t<e.length;t++)e[t].lock()},e.exports=u},1269:(e,t,r)=>{"use strict";var n=r(3078),i=r(3890);t.generateWorker=function(e,t,r){var a=new i(t.streamFiles,r,t.platform,t.encodeFileName),s=0;try{e.forEach((function(e,r){s++;var i=function(e,t){var r=e||t,i=n[r];if(!i)throw new Error(r+" is not a valid compression method !");return i}(r.options.compression,t.compression),o=r.options.compressionOptions||t.compressionOptions||{},l=r.dir,h=r.date;r._compressWorker(i,o).withStreamInfo("file",{name:e,dir:l,date:h,comment:r.comment||"",unixPermissions:r.unixPermissions,dosPermissions:r.dosPermissions}).pipe(a)})),a.entriesCount=s}catch(e){a.error(e)}return a}},8833:(e,t,r)=>{"use strict";function n(){if(!(this instanceof n))return new n;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var e=new n;for(var t in this)"function"!=typeof this[t]&&(e[t]=this[t]);return e}}n.prototype=r(8442),n.prototype.loadAsync=r(629),n.support=r(6954),n.defaults=r(5051),n.version="3.10.1",n.loadAsync=function(e,t){return(new n).loadAsync(e,t)},n.external=r(7882),e.exports=n},629:(e,t,r)=>{"use strict";var n=r(1132),i=r(7882),a=r(8222),s=r(7548),o=r(1919),l=r(417);function h(e){return new i.Promise((function(t,r){var n=e.decompressed.getContentWorker().pipe(new o);n.on("error",(function(e){r(e)})).on("end",(function(){n.streamInfo.crc32!==e.decompressed.crc32?r(new Error("Corrupted zip : CRC32 mismatch")):t()})).resume()}))}e.exports=function(e,t){var r=this;return t=n.extend(t||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:a.utf8decode}),l.isNode&&l.isStream(e)?i.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):n.prepareContent("the loaded zip file",e,!0,t.optimizedBinaryString,t.base64).then((function(e){var r=new s(t);return r.load(e),r})).then((function(e){var r=[i.Promise.resolve(e)],n=e.files;if(t.checkCRC32)for(var a=0;a<n.length;a++)r.push(h(n[a]));return i.Promise.all(r)})).then((function(e){for(var i=e.shift(),a=i.files,s=0;s<a.length;s++){var o=a[s],l=o.fileNameStr,h=n.resolve(o.fileNameStr);r.file(h,o.decompressed,{binary:!0,optimizedBinaryString:!0,date:o.date,dir:o.dir,comment:o.fileCommentStr.length?o.fileCommentStr:null,unixPermissions:o.unixPermissions,dosPermissions:o.dosPermissions,createFolders:t.createFolders}),o.dir||(r.file(h).unsafeOriginalName=l)}return i.zipComment.length&&(r.comment=i.zipComment),r}))}},417:e=>{"use strict";e.exports={isNode:"undefined"!=typeof Buffer,newBufferFrom:function(e,t){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(e,t);if("number"==typeof e)throw new Error('The "data" argument must not be a number');return new Buffer(e,t)},allocBuffer:function(e){if(Buffer.alloc)return Buffer.alloc(e);var t=new Buffer(e);return t.fill(0),t},isBuffer:function(e){return Buffer.isBuffer(e)},isStream:function(e){return e&&"function"==typeof e.on&&"function"==typeof e.pause&&"function"==typeof e.resume}}},905:(e,t,r)=>{"use strict";var n=r(1132),i=r(193);function a(e,t){i.call(this,"Nodejs stream input adapter for "+e),this._upstreamEnded=!1,this._bindStream(t)}n.inherits(a,i),a.prototype._bindStream=function(e){var t=this;this._stream=e,e.pause(),e.on("data",(function(e){t.push({data:e,meta:{percent:0}})})).on("error",(function(e){t.isPaused?this.generatedError=e:t.error(e)})).on("end",(function(){t.isPaused?t._upstreamEnded=!0:t.end()}))},a.prototype.pause=function(){return!!i.prototype.pause.call(this)&&(this._stream.pause(),!0)},a.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},e.exports=a},4644:(e,t,r)=>{"use strict";var n=r(4198).Readable;function i(e,t,r){n.call(this,t),this._helper=e;var i=this;e.on("data",(function(e,t){i.push(e)||i._helper.pause(),r&&r(t)})).on("error",(function(e){i.emit("error",e)})).on("end",(function(){i.push(null)}))}r(1132).inherits(i,n),i.prototype._read=function(){this._helper.resume()},e.exports=i},8442:(e,t,r)=>{"use strict";var n=r(8222),i=r(1132),a=r(193),s=r(8648),o=r(5051),l=r(8807),h=r(9985),c=r(1269),u=r(417),d=r(905),f=function(e,t,r){var n,s=i.getTypeOf(t),c=i.extend(r||{},o);c.date=c.date||new Date,null!==c.compression&&(c.compression=c.compression.toUpperCase()),"string"==typeof c.unixPermissions&&(c.unixPermissions=parseInt(c.unixPermissions,8)),c.unixPermissions&&16384&c.unixPermissions&&(c.dir=!0),c.dosPermissions&&16&c.dosPermissions&&(c.dir=!0),c.dir&&(e=m(e)),c.createFolders&&(n=p(e))&&g.call(this,n,!0);var f="string"===s&&!1===c.binary&&!1===c.base64;r&&void 0!==r.binary||(c.binary=!f),(t instanceof l&&0===t.uncompressedSize||c.dir||!t||0===t.length)&&(c.base64=!1,c.binary=!0,t="",c.compression="STORE",s="string");var b;b=t instanceof l||t instanceof a?t:u.isNode&&u.isStream(t)?new d(e,t):i.prepareContent(e,t,c.binary,c.optimizedBinaryString,c.base64);var _=new h(e,b,c);this.files[e]=_},p=function(e){"/"===e.slice(-1)&&(e=e.substring(0,e.length-1));var t=e.lastIndexOf("/");return t>0?e.substring(0,t):""},m=function(e){return"/"!==e.slice(-1)&&(e+="/"),e},g=function(e,t){return t=void 0!==t?t:o.createFolders,e=m(e),this.files[e]||f.call(this,e,null,{dir:!0,createFolders:t}),this.files[e]};function b(e){return"[object RegExp]"===Object.prototype.toString.call(e)}var _={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(e){var t,r,n;for(t in this.files)n=this.files[t],(r=t.slice(this.root.length,t.length))&&t.slice(0,this.root.length)===this.root&&e(r,n)},filter:function(e){var t=[];return this.forEach((function(r,n){e(r,n)&&t.push(n)})),t},file:function(e,t,r){if(1===arguments.length){if(b(e)){var n=e;return this.filter((function(e,t){return!t.dir&&n.test(e)}))}var i=this.files[this.root+e];return i&&!i.dir?i:null}return e=this.root+e,f.call(this,e,t,r),this},folder:function(e){if(!e)return this;if(b(e))return this.filter((function(t,r){return r.dir&&e.test(t)}));var t=this.root+e,r=g.call(this,t),n=this.clone();return n.root=r.name,n},remove:function(e){e=this.root+e;var t=this.files[e];if(t||("/"!==e.slice(-1)&&(e+="/"),t=this.files[e]),t&&!t.dir)delete this.files[e];else for(var r=this.filter((function(t,r){return r.name.slice(0,e.length)===e})),n=0;n<r.length;n++)delete this.files[r[n].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(e){var t,r={};try{if((r=i.extend(e||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:n.utf8encode})).type=r.type.toLowerCase(),r.compression=r.compression.toUpperCase(),"binarystring"===r.type&&(r.type="string"),!r.type)throw new Error("No output type specified.");i.checkSupport(r.type),"darwin"!==r.platform&&"freebsd"!==r.platform&&"linux"!==r.platform&&"sunos"!==r.platform||(r.platform="UNIX"),"win32"===r.platform&&(r.platform="DOS");var o=r.comment||this.comment||"";t=c.generateWorker(this,r,o)}catch(e){(t=new a("error")).error(e)}return new s(t,r.type||"string",r.mimeType)},generateAsync:function(e,t){return this.generateInternalStream(e).accumulate(t)},generateNodeStream:function(e,t){return(e=e||{}).type||(e.type="nodebuffer"),this.generateInternalStream(e).toNodejsStream(t)}};e.exports=_},1191:(e,t,r)=>{"use strict";var n=r(5074);function i(e){n.call(this,e);for(var t=0;t<this.data.length;t++)e[t]=255&e[t]}r(1132).inherits(i,n),i.prototype.byteAt=function(e){return this.data[this.zero+e]},i.prototype.lastIndexOfSignature=function(e){for(var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),i=e.charCodeAt(3),a=this.length-4;a>=0;--a)if(this.data[a]===t&&this.data[a+1]===r&&this.data[a+2]===n&&this.data[a+3]===i)return a-this.zero;return-1},i.prototype.readAndCheckSignature=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),i=e.charCodeAt(3),a=this.readData(4);return t===a[0]&&r===a[1]&&n===a[2]&&i===a[3]},i.prototype.readData=function(e){if(this.checkOffset(e),0===e)return[];var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},e.exports=i},5074:(e,t,r)=>{"use strict";var n=r(1132);function i(e){this.data=e,this.length=e.length,this.index=0,this.zero=0}i.prototype={checkOffset:function(e){this.checkIndex(this.index+e)},checkIndex:function(e){if(this.length<this.zero+e||e<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+e+"). Corrupted zip ?")},setIndex:function(e){this.checkIndex(e),this.index=e},skip:function(e){this.setIndex(this.index+e)},byteAt:function(){},readInt:function(e){var t,r=0;for(this.checkOffset(e),t=this.index+e-1;t>=this.index;t--)r=(r<<8)+this.byteAt(t);return this.index+=e,r},readString:function(e){return n.transformTo("string",this.readData(e))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var e=this.readInt(4);return new Date(Date.UTC(1980+(e>>25&127),(e>>21&15)-1,e>>16&31,e>>11&31,e>>5&63,(31&e)<<1))}},e.exports=i},2916:(e,t,r)=>{"use strict";var n=r(7959);function i(e){n.call(this,e)}r(1132).inherits(i,n),i.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},e.exports=i},9663:(e,t,r)=>{"use strict";var n=r(5074);function i(e){n.call(this,e)}r(1132).inherits(i,n),i.prototype.byteAt=function(e){return this.data.charCodeAt(this.zero+e)},i.prototype.lastIndexOfSignature=function(e){return this.data.lastIndexOf(e)-this.zero},i.prototype.readAndCheckSignature=function(e){return e===this.readData(4)},i.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},e.exports=i},7959:(e,t,r)=>{"use strict";var n=r(1191);function i(e){n.call(this,e)}r(1132).inherits(i,n),i.prototype.readData=function(e){if(this.checkOffset(e),0===e)return new Uint8Array(0);var t=this.data.subarray(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},e.exports=i},9483:(e,t,r)=>{"use strict";var n=r(1132),i=r(6954),a=r(1191),s=r(9663),o=r(2916),l=r(7959);e.exports=function(e){var t=n.getTypeOf(e);return n.checkSupport(t),"string"!==t||i.uint8array?"nodebuffer"===t?new o(e):i.uint8array?new l(n.transformTo("uint8array",e)):new a(n.transformTo("array",e)):new s(e)}},6407:(e,t)=>{"use strict";t.LOCAL_FILE_HEADER="PK",t.CENTRAL_FILE_HEADER="PK",t.CENTRAL_DIRECTORY_END="PK",t.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",t.ZIP64_CENTRAL_DIRECTORY_END="PK",t.DATA_DESCRIPTOR="PK\b"},1019:(e,t,r)=>{"use strict";var n=r(193),i=r(1132);function a(e){n.call(this,"ConvertWorker to "+e),this.destType=e}i.inherits(a,n),a.prototype.processChunk=function(e){this.push({data:i.transformTo(this.destType,e.data),meta:e.meta})},e.exports=a},1919:(e,t,r)=>{"use strict";var n=r(193),i=r(8786);function a(){n.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}r(1132).inherits(a,n),a.prototype.processChunk=function(e){this.streamInfo.crc32=i(e.data,this.streamInfo.crc32||0),this.push(e)},e.exports=a},8432:(e,t,r)=>{"use strict";var n=r(1132),i=r(193);function a(e){i.call(this,"DataLengthProbe for "+e),this.propName=e,this.withStreamInfo(e,0)}n.inherits(a,i),a.prototype.processChunk=function(e){if(e){var t=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=t+e.data.length}i.prototype.processChunk.call(this,e)},e.exports=a},4982:(e,t,r)=>{"use strict";var n=r(1132),i=r(193);function a(e){i.call(this,"DataWorker");var t=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,e.then((function(e){t.dataIsReady=!0,t.data=e,t.max=e&&e.length||0,t.type=n.getTypeOf(e),t.isPaused||t._tickAndRepeat()}),(function(e){t.error(e)}))}n.inherits(a,i),a.prototype.cleanUp=function(){i.prototype.cleanUp.call(this),this.data=null},a.prototype.resume=function(){return!!i.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,n.delay(this._tickAndRepeat,[],this)),!0)},a.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(n.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},a.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var e=null,t=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":e=this.data.substring(this.index,t);break;case"uint8array":e=this.data.subarray(this.index,t);break;case"array":case"nodebuffer":e=this.data.slice(this.index,t)}return this.index=t,this.push({data:e,meta:{percent:this.max?this.index/this.max*100:0}})},e.exports=a},193:e=>{"use strict";function t(e){this.name=e||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}t.prototype={push:function(e){this.emit("data",e)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(e){this.emit("error",e)}return!0},error:function(e){return!this.isFinished&&(this.isPaused?this.generatedError=e:(this.isFinished=!0,this.emit("error",e),this.previous&&this.previous.error(e),this.cleanUp()),!0)},on:function(e,t){return this._listeners[e].push(t),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(e,t){if(this._listeners[e])for(var r=0;r<this._listeners[e].length;r++)this._listeners[e][r].call(this,t)},pipe:function(e){return e.registerPrevious(this)},registerPrevious:function(e){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=e.streamInfo,this.mergeStreamInfo(),this.previous=e;var t=this;return e.on("data",(function(e){t.processChunk(e)})),e.on("end",(function(){t.end()})),e.on("error",(function(e){t.error(e)})),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;this.isPaused=!1;var e=!1;return this.generatedError&&(this.error(this.generatedError),e=!0),this.previous&&this.previous.resume(),!e},flush:function(){},processChunk:function(e){this.push(e)},withStreamInfo:function(e,t){return this.extraStreamInfo[e]=t,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var e in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,e)&&(this.streamInfo[e]=this.extraStreamInfo[e])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var e="Worker "+this.name;return this.previous?this.previous+" -> "+e:e}},e.exports=t},8648:(e,t,r)=>{"use strict";var n=r(1132),i=r(1019),a=r(193),s=r(2678),o=r(6954),l=r(7882),h=null;if(o.nodestream)try{h=r(4644)}catch(e){}function c(e,t,r){var s=t;switch(t){case"blob":case"arraybuffer":s="uint8array";break;case"base64":s="string"}try{this._internalType=s,this._outputType=t,this._mimeType=r,n.checkSupport(s),this._worker=e.pipe(new i(s)),e.lock()}catch(e){this._worker=new a("error"),this._worker.error(e)}}c.prototype={accumulate:function(e){return t=this,r=e,new l.Promise((function(e,i){var a=[],o=t._internalType,l=t._outputType,h=t._mimeType;t.on("data",(function(e,t){a.push(e),r&&r(t)})).on("error",(function(e){a=[],i(e)})).on("end",(function(){try{var t=function(e,t,r){switch(e){case"blob":return n.newBlob(n.transformTo("arraybuffer",t),r);case"base64":return s.encode(t);default:return n.transformTo(e,t)}}(l,function(e,t){var r,n=0,i=null,a=0;for(r=0;r<t.length;r++)a+=t[r].length;switch(e){case"string":return t.join("");case"array":return Array.prototype.concat.apply([],t);case"uint8array":for(i=new Uint8Array(a),r=0;r<t.length;r++)i.set(t[r],n),n+=t[r].length;return i;case"nodebuffer":return Buffer.concat(t);default:throw new Error("concat : unsupported type '"+e+"'")}}(o,a),h);e(t)}catch(e){i(e)}a=[]})).resume()}));var t,r},on:function(e,t){var r=this;return"data"===e?this._worker.on(e,(function(e){t.call(r,e.data,e.meta)})):this._worker.on(e,(function(){n.delay(t,arguments,r)})),this},resume:function(){return n.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(e){if(n.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw new Error(this._outputType+" is not supported by this method");return new h(this,{objectMode:"nodebuffer"!==this._outputType},e)}},e.exports=c},6954:(e,t,r)=>{"use strict";if(t.base64=!0,t.array=!0,t.string=!0,t.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,t.nodebuffer="undefined"!=typeof Buffer,t.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)t.blob=!1;else{var n=new ArrayBuffer(0);try{t.blob=0===new Blob([n],{type:"application/zip"}).size}catch(e){try{var i=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);i.append(n),t.blob=0===i.getBlob("application/zip").size}catch(e){t.blob=!1}}}try{t.nodestream=!!r(4198).Readable}catch(e){t.nodestream=!1}},8222:(e,t,r)=>{"use strict";for(var n=r(1132),i=r(6954),a=r(417),s=r(193),o=new Array(256),l=0;l<256;l++)o[l]=l>=252?6:l>=248?5:l>=240?4:l>=224?3:l>=192?2:1;function h(){s.call(this,"utf-8 decode"),this.leftOver=null}function c(){s.call(this,"utf-8 encode")}o[254]=o[254]=1,t.utf8encode=function(e){return i.nodebuffer?a.newBufferFrom(e,"utf-8"):function(e){var t,r,n,a,s,o=e.length,l=0;for(a=0;a<o;a++)55296==(64512&(r=e.charCodeAt(a)))&&a+1<o&&56320==(64512&(n=e.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(n-56320),a++),l+=r<128?1:r<2048?2:r<65536?3:4;for(t=i.uint8array?new Uint8Array(l):new Array(l),s=0,a=0;s<l;a++)55296==(64512&(r=e.charCodeAt(a)))&&a+1<o&&56320==(64512&(n=e.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(n-56320),a++),r<128?t[s++]=r:r<2048?(t[s++]=192|r>>>6,t[s++]=128|63&r):r<65536?(t[s++]=224|r>>>12,t[s++]=128|r>>>6&63,t[s++]=128|63&r):(t[s++]=240|r>>>18,t[s++]=128|r>>>12&63,t[s++]=128|r>>>6&63,t[s++]=128|63&r);return t}(e)},t.utf8decode=function(e){return i.nodebuffer?n.transformTo("nodebuffer",e).toString("utf-8"):function(e){var t,r,i,a,s=e.length,l=new Array(2*s);for(r=0,t=0;t<s;)if((i=e[t++])<128)l[r++]=i;else if((a=o[i])>4)l[r++]=65533,t+=a-1;else{for(i&=2===a?31:3===a?15:7;a>1&&t<s;)i=i<<6|63&e[t++],a--;a>1?l[r++]=65533:i<65536?l[r++]=i:(i-=65536,l[r++]=55296|i>>10&1023,l[r++]=56320|1023&i)}return l.length!==r&&(l.subarray?l=l.subarray(0,r):l.length=r),n.applyFromCharCode(l)}(e=n.transformTo(i.uint8array?"uint8array":"array",e))},n.inherits(h,s),h.prototype.processChunk=function(e){var r=n.transformTo(i.uint8array?"uint8array":"array",e.data);if(this.leftOver&&this.leftOver.length){if(i.uint8array){var a=r;(r=new Uint8Array(a.length+this.leftOver.length)).set(this.leftOver,0),r.set(a,this.leftOver.length)}else r=this.leftOver.concat(r);this.leftOver=null}var s=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;r>=0&&128==(192&e[r]);)r--;return r<0||0===r?t:r+o[e[r]]>t?r:t}(r),l=r;s!==r.length&&(i.uint8array?(l=r.subarray(0,s),this.leftOver=r.subarray(s,r.length)):(l=r.slice(0,s),this.leftOver=r.slice(s,r.length))),this.push({data:t.utf8decode(l),meta:e.meta})},h.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:t.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},t.Utf8DecodeWorker=h,n.inherits(c,s),c.prototype.processChunk=function(e){this.push({data:t.utf8encode(e.data),meta:e.meta})},t.Utf8EncodeWorker=c},1132:(e,t,r)=>{"use strict";var n=r(6954),i=r(2678),a=r(417),s=r(7882);function o(e){return e}function l(e,t){for(var r=0;r<e.length;++r)t[r]=255&e.charCodeAt(r);return t}r(2791),t.newBlob=function(e,r){t.checkSupport("blob");try{return new Blob([e],{type:r})}catch(t){try{var n=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return n.append(e),n.getBlob(r)}catch(e){throw new Error("Bug : can't construct the Blob.")}}};var h={stringifyByChunk:function(e,t,r){var n=[],i=0,a=e.length;if(a<=r)return String.fromCharCode.apply(null,e);for(;i<a;)"array"===t||"nodebuffer"===t?n.push(String.fromCharCode.apply(null,e.slice(i,Math.min(i+r,a)))):n.push(String.fromCharCode.apply(null,e.subarray(i,Math.min(i+r,a)))),i+=r;return n.join("")},stringifyByChar:function(e){for(var t="",r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t},applyCanBeUsed:{uint8array:function(){try{return n.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(e){return!1}}(),nodebuffer:function(){try{return n.nodebuffer&&1===String.fromCharCode.apply(null,a.allocBuffer(1)).length}catch(e){return!1}}()}};function c(e){var r=65536,n=t.getTypeOf(e),i=!0;if("uint8array"===n?i=h.applyCanBeUsed.uint8array:"nodebuffer"===n&&(i=h.applyCanBeUsed.nodebuffer),i)for(;r>1;)try{return h.stringifyByChunk(e,n,r)}catch(e){r=Math.floor(r/2)}return h.stringifyByChar(e)}function u(e,t){for(var r=0;r<e.length;r++)t[r]=e[r];return t}t.applyFromCharCode=c;var d={};d.string={string:o,array:function(e){return l(e,new Array(e.length))},arraybuffer:function(e){return d.string.uint8array(e).buffer},uint8array:function(e){return l(e,new Uint8Array(e.length))},nodebuffer:function(e){return l(e,a.allocBuffer(e.length))}},d.array={string:c,array:o,arraybuffer:function(e){return new Uint8Array(e).buffer},uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return a.newBufferFrom(e)}},d.arraybuffer={string:function(e){return c(new Uint8Array(e))},array:function(e){return u(new Uint8Array(e),new Array(e.byteLength))},arraybuffer:o,uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return a.newBufferFrom(new Uint8Array(e))}},d.uint8array={string:c,array:function(e){return u(e,new Array(e.length))},arraybuffer:function(e){return e.buffer},uint8array:o,nodebuffer:function(e){return a.newBufferFrom(e)}},d.nodebuffer={string:c,array:function(e){return u(e,new Array(e.length))},arraybuffer:function(e){return d.nodebuffer.uint8array(e).buffer},uint8array:function(e){return u(e,new Uint8Array(e.length))},nodebuffer:o},t.transformTo=function(e,r){if(r||(r=""),!e)return r;t.checkSupport(e);var n=t.getTypeOf(r);return d[n][e](r)},t.resolve=function(e){for(var t=e.split("/"),r=[],n=0;n<t.length;n++){var i=t[n];"."===i||""===i&&0!==n&&n!==t.length-1||(".."===i?r.pop():r.push(i))}return r.join("/")},t.getTypeOf=function(e){return"string"==typeof e?"string":"[object Array]"===Object.prototype.toString.call(e)?"array":n.nodebuffer&&a.isBuffer(e)?"nodebuffer":n.uint8array&&e instanceof Uint8Array?"uint8array":n.arraybuffer&&e instanceof ArrayBuffer?"arraybuffer":void 0},t.checkSupport=function(e){if(!n[e.toLowerCase()])throw new Error(e+" is not supported by this platform")},t.MAX_VALUE_16BITS=65535,t.MAX_VALUE_32BITS=-1,t.pretty=function(e){var t,r,n="";for(r=0;r<(e||"").length;r++)n+="\\x"+((t=e.charCodeAt(r))<16?"0":"")+t.toString(16).toUpperCase();return n},t.delay=function(e,t,r){setImmediate((function(){e.apply(r||null,t||[])}))},t.inherits=function(e,t){var r=function(){};r.prototype=t.prototype,e.prototype=new r},t.extend=function(){var e,t,r={};for(e=0;e<arguments.length;e++)for(t in arguments[e])Object.prototype.hasOwnProperty.call(arguments[e],t)&&void 0===r[t]&&(r[t]=arguments[e][t]);return r},t.prepareContent=function(e,r,a,o,h){return s.Promise.resolve(r).then((function(e){return n.blob&&(e instanceof Blob||-1!==["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(e)))&&"undefined"!=typeof FileReader?new s.Promise((function(t,r){var n=new FileReader;n.onload=function(e){t(e.target.result)},n.onerror=function(e){r(e.target.error)},n.readAsArrayBuffer(e)})):e})).then((function(r){var c,u=t.getTypeOf(r);return u?("arraybuffer"===u?r=t.transformTo("uint8array",r):"string"===u&&(h?r=i.decode(r):a&&!0!==o&&(r=l(c=r,n.uint8array?new Uint8Array(c.length):new Array(c.length)))),r):s.Promise.reject(new Error("Can't read the data of '"+e+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))}))}},7548:(e,t,r)=>{"use strict";var n=r(9483),i=r(1132),a=r(6407),s=r(7404),o=r(6954);function l(e){this.files=[],this.loadOptions=e}l.prototype={checkSignature:function(e){if(!this.reader.readAndCheckSignature(e)){this.reader.index-=4;var t=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+i.pretty(t)+", expected "+i.pretty(e)+")")}},isSignature:function(e,t){var r=this.reader.index;this.reader.setIndex(e);var n=this.reader.readString(4)===t;return this.reader.setIndex(r),n},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var e=this.reader.readData(this.zipCommentLength),t=o.uint8array?"uint8array":"array",r=i.transformTo(t,e);this.zipComment=this.loadOptions.decodeFileName(r)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var e,t,r,n=this.zip64EndOfCentralSize-44;0<n;)e=this.reader.readInt(2),t=this.reader.readInt(4),r=this.reader.readData(t),this.zip64ExtensibleData[e]={id:e,length:t,value:r}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),this.disksCount>1)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var e,t;for(e=0;e<this.files.length;e++)t=this.files[e],this.reader.setIndex(t.localHeaderOffset),this.checkSignature(a.LOCAL_FILE_HEADER),t.readLocalPart(this.reader),t.handleUTF8(),t.processAttributes()},readCentralDir:function(){var e;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(a.CENTRAL_FILE_HEADER);)(e=new s({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(e);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var e=this.reader.lastIndexOfSignature(a.CENTRAL_DIRECTORY_END);if(e<0)throw this.isSignature(0,a.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(e);var t=e;if(this.checkSignature(a.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===i.MAX_VALUE_16BITS||this.diskWithCentralDirStart===i.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===i.MAX_VALUE_16BITS||this.centralDirRecords===i.MAX_VALUE_16BITS||this.centralDirSize===i.MAX_VALUE_32BITS||this.centralDirOffset===i.MAX_VALUE_32BITS){if(this.zip64=!0,(e=this.reader.lastIndexOfSignature(a.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(e),this.checkSignature(a.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,a.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(a.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(a.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var r=this.centralDirOffset+this.centralDirSize;this.zip64&&(r+=20,r+=12+this.zip64EndOfCentralSize);var n=t-r;if(n>0)this.isSignature(t,a.CENTRAL_FILE_HEADER)||(this.reader.zero=n);else if(n<0)throw new Error("Corrupted zip: missing "+Math.abs(n)+" bytes.")},prepareReader:function(e){this.reader=n(e)},load:function(e){this.prepareReader(e),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},e.exports=l},7404:(e,t,r)=>{"use strict";var n=r(9483),i=r(1132),a=r(8807),s=r(8786),o=r(8222),l=r(3078),h=r(6954);function c(e,t){this.options=e,this.loadOptions=t}c.prototype={isEncrypted:function(){return!(1&~this.bitFlag)},useUTF8:function(){return!(2048&~this.bitFlag)},readLocalPart:function(e){var t,r;if(e.skip(22),this.fileNameLength=e.readInt(2),r=e.readInt(2),this.fileName=e.readData(this.fileNameLength),e.skip(r),-1===this.compressedSize||-1===this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(null===(t=function(e){for(var t in l)if(Object.prototype.hasOwnProperty.call(l,t)&&l[t].magic===e)return l[t];return null}(this.compressionMethod)))throw new Error("Corrupted zip : compression "+i.pretty(this.compressionMethod)+" unknown (inner file : "+i.transformTo("string",this.fileName)+")");this.decompressed=new a(this.compressedSize,this.uncompressedSize,this.crc32,t,e.readData(this.compressedSize))},readCentralPart:function(e){this.versionMadeBy=e.readInt(2),e.skip(2),this.bitFlag=e.readInt(2),this.compressionMethod=e.readString(2),this.date=e.readDate(),this.crc32=e.readInt(4),this.compressedSize=e.readInt(4),this.uncompressedSize=e.readInt(4);var t=e.readInt(2);if(this.extraFieldsLength=e.readInt(2),this.fileCommentLength=e.readInt(2),this.diskNumberStart=e.readInt(2),this.internalFileAttributes=e.readInt(2),this.externalFileAttributes=e.readInt(4),this.localHeaderOffset=e.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");e.skip(t),this.readExtraFields(e),this.parseZIP64ExtraField(e),this.fileComment=e.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var e=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0===e&&(this.dosPermissions=63&this.externalFileAttributes),3===e&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var e=n(this.extraFields[1].value);this.uncompressedSize===i.MAX_VALUE_32BITS&&(this.uncompressedSize=e.readInt(8)),this.compressedSize===i.MAX_VALUE_32BITS&&(this.compressedSize=e.readInt(8)),this.localHeaderOffset===i.MAX_VALUE_32BITS&&(this.localHeaderOffset=e.readInt(8)),this.diskNumberStart===i.MAX_VALUE_32BITS&&(this.diskNumberStart=e.readInt(4))}},readExtraFields:function(e){var t,r,n,i=e.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});e.index+4<i;)t=e.readInt(2),r=e.readInt(2),n=e.readData(r),this.extraFields[t]={id:t,length:r,value:n};e.setIndex(i)},handleUTF8:function(){var e=h.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=o.utf8decode(this.fileName),this.fileCommentStr=o.utf8decode(this.fileComment);else{var t=this.findExtraFieldUnicodePath();if(null!==t)this.fileNameStr=t;else{var r=i.transformTo(e,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(r)}var n=this.findExtraFieldUnicodeComment();if(null!==n)this.fileCommentStr=n;else{var a=i.transformTo(e,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(a)}}},findExtraFieldUnicodePath:function(){var e=this.extraFields[28789];if(e){var t=n(e.value);return 1!==t.readInt(1)||s(this.fileName)!==t.readInt(4)?null:o.utf8decode(t.readData(e.length-5))}return null},findExtraFieldUnicodeComment:function(){var e=this.extraFields[25461];if(e){var t=n(e.value);return 1!==t.readInt(1)||s(this.fileComment)!==t.readInt(4)?null:o.utf8decode(t.readData(e.length-5))}return null}},e.exports=c},9985:(e,t,r)=>{"use strict";var n=r(8648),i=r(4982),a=r(8222),s=r(8807),o=r(193),l=function(e,t,r){this.name=e,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this.unixPermissions=r.unixPermissions,this.dosPermissions=r.dosPermissions,this._data=t,this._dataBinary=r.binary,this.options={compression:r.compression,compressionOptions:r.compressionOptions}};l.prototype={internalStream:function(e){var t=null,r="string";try{if(!e)throw new Error("No output type specified.");var i="string"===(r=e.toLowerCase())||"text"===r;"binarystring"!==r&&"text"!==r||(r="string"),t=this._decompressWorker();var s=!this._dataBinary;s&&!i&&(t=t.pipe(new a.Utf8EncodeWorker)),!s&&i&&(t=t.pipe(new a.Utf8DecodeWorker))}catch(e){(t=new o("error")).error(e)}return new n(t,r,"")},async:function(e,t){return this.internalStream(e).accumulate(t)},nodeStream:function(e,t){return this.internalStream(e||"nodebuffer").toNodejsStream(t)},_compressWorker:function(e,t){if(this._data instanceof s&&this._data.compression.magic===e.magic)return this._data.getCompressedWorker();var r=this._decompressWorker();return this._dataBinary||(r=r.pipe(new a.Utf8EncodeWorker)),s.createWorkerFrom(r,e,t)},_decompressWorker:function(){return this._data instanceof s?this._data.getContentWorker():this._data instanceof o?this._data:new i(this._data)}};for(var h=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],c=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},u=0;u<h.length;u++)l.prototype[h[u]]=c;e.exports=l},9977:(e,t,r)=>{"use strict";var n=r(874);function i(){}var a={},s=["REJECTED"],o=["FULFILLED"],l=["PENDING"];if(!process.browser)var h=["UNHANDLED"];function c(e){if("function"!=typeof e)throw new TypeError("resolver must be a function");this.state=l,this.queue=[],this.outcome=void 0,process.browser||(this.handled=h),e!==i&&p(this,e)}function u(e,t,r){this.promise=e,"function"==typeof t&&(this.onFulfilled=t,this.callFulfilled=this.otherCallFulfilled),"function"==typeof r&&(this.onRejected=r,this.callRejected=this.otherCallRejected)}function d(e,t,r){n((function(){var n;try{n=t(r)}catch(t){return a.reject(e,t)}n===e?a.reject(e,new TypeError("Cannot resolve promise with itself")):a.resolve(e,n)}))}function f(e){var t=e&&e.then;if(e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof t)return function(){t.apply(e,arguments)}}function p(e,t){var r=!1;function n(t){r||(r=!0,a.reject(e,t))}function i(t){r||(r=!0,a.resolve(e,t))}var s=m((function(){t(i,n)}));"error"===s.status&&n(s.value)}function m(e,t){var r={};try{r.value=e(t),r.status="success"}catch(e){r.status="error",r.value=e}return r}e.exports=c,c.prototype.finally=function(e){if("function"!=typeof e)return this;var t=this.constructor;return this.then((function(r){return t.resolve(e()).then((function(){return r}))}),(function(r){return t.resolve(e()).then((function(){throw r}))}))},c.prototype.catch=function(e){return this.then(null,e)},c.prototype.then=function(e,t){if("function"!=typeof e&&this.state===o||"function"!=typeof t&&this.state===s)return this;var r=new this.constructor(i);return process.browser||this.handled===h&&(this.handled=null),this.state!==l?d(r,this.state===o?e:t,this.outcome):this.queue.push(new u(r,e,t)),r},u.prototype.callFulfilled=function(e){a.resolve(this.promise,e)},u.prototype.otherCallFulfilled=function(e){d(this.promise,this.onFulfilled,e)},u.prototype.callRejected=function(e){a.reject(this.promise,e)},u.prototype.otherCallRejected=function(e){d(this.promise,this.onRejected,e)},a.resolve=function(e,t){var r=m(f,t);if("error"===r.status)return a.reject(e,r.value);var n=r.value;if(n)p(e,n);else{e.state=o,e.outcome=t;for(var i=-1,s=e.queue.length;++i<s;)e.queue[i].callFulfilled(t)}return e},a.reject=function(e,t){e.state=s,e.outcome=t,process.browser||e.handled===h&&n((function(){e.handled===h&&process.emit("unhandledRejection",t,e)}));for(var r=-1,i=e.queue.length;++r<i;)e.queue[r].callRejected(t);return e},c.resolve=function(e){return e instanceof this?e:a.resolve(new this(i),e)},c.reject=function(e){var t=new this(i);return a.reject(t,e)},c.all=function(e){var t=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var r=e.length,n=!1;if(!r)return this.resolve([]);for(var s=new Array(r),o=0,l=-1,h=new this(i);++l<r;)c(e[l],l);return h;function c(e,i){t.resolve(e).then((function(e){s[i]=e,++o!==r||n||(n=!0,a.resolve(h,s))}),(function(e){n||(n=!0,a.reject(h,e))}))}},c.race=function(e){if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var t=e.length,r=!1;if(!t)return this.resolve([]);for(var n,s=-1,o=new this(i);++s<t;)n=e[s],this.resolve(n).then((function(e){r||(r=!0,a.resolve(o,e))}),(function(e){r||(r=!0,a.reject(o,e))}));return o}},1668:(e,t,r)=>{"use strict";var n={};(0,r(9805).assign)(n,r(3303),r(7083),r(9681)),e.exports=n},3303:(e,t,r)=>{"use strict";var n=r(8411),i=r(9805),a=r(1996),s=r(4674),o=r(4442),l=Object.prototype.toString,h=0,c=-1,u=0,d=8;function f(e){if(!(this instanceof f))return new f(e);this.options=i.assign({level:c,method:d,chunkSize:16384,windowBits:15,memLevel:8,strategy:u,to:""},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new o,this.strm.avail_out=0;var r=n.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(r!==h)throw new Error(s[r]);if(t.header&&n.deflateSetHeader(this.strm,t.header),t.dictionary){var p;if(p="string"==typeof t.dictionary?a.string2buf(t.dictionary):"[object ArrayBuffer]"===l.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(r=n.deflateSetDictionary(this.strm,p))!==h)throw new Error(s[r]);this._dict_set=!0}}function p(e,t){var r=new f(t);if(r.push(e,!0),r.err)throw r.msg||s[r.err];return r.result}f.prototype.push=function(e,t){var r,s,o=this.strm,c=this.options.chunkSize;if(this.ended)return!1;s=t===~~t?t:!0===t?4:0,"string"==typeof e?o.input=a.string2buf(e):"[object ArrayBuffer]"===l.call(e)?o.input=new Uint8Array(e):o.input=e,o.next_in=0,o.avail_in=o.input.length;do{if(0===o.avail_out&&(o.output=new i.Buf8(c),o.next_out=0,o.avail_out=c),1!==(r=n.deflate(o,s))&&r!==h)return this.onEnd(r),this.ended=!0,!1;0!==o.avail_out&&(0!==o.avail_in||4!==s&&2!==s)||("string"===this.options.to?this.onData(a.buf2binstring(i.shrinkBuf(o.output,o.next_out))):this.onData(i.shrinkBuf(o.output,o.next_out)))}while((o.avail_in>0||0===o.avail_out)&&1!==r);return 4===s?(r=n.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===h):2!==s||(this.onEnd(h),o.avail_out=0,!0)},f.prototype.onData=function(e){this.chunks.push(e)},f.prototype.onEnd=function(e){e===h&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},t.Deflate=f,t.deflate=p,t.deflateRaw=function(e,t){return(t=t||{}).raw=!0,p(e,t)},t.gzip=function(e,t){return(t=t||{}).gzip=!0,p(e,t)}},7083:(e,t,r)=>{"use strict";var n=r(1447),i=r(9805),a=r(1996),s=r(9681),o=r(4674),l=r(4442),h=r(7414),c=Object.prototype.toString;function u(e){if(!(this instanceof u))return new u(e);this.options=i.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&(15&t.windowBits||(t.windowBits|=15)),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var r=n.inflateInit2(this.strm,t.windowBits);if(r!==s.Z_OK)throw new Error(o[r]);if(this.header=new h,n.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=a.string2buf(t.dictionary):"[object ArrayBuffer]"===c.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(r=n.inflateSetDictionary(this.strm,t.dictionary))!==s.Z_OK))throw new Error(o[r])}function d(e,t){var r=new u(t);if(r.push(e,!0),r.err)throw r.msg||o[r.err];return r.result}u.prototype.push=function(e,t){var r,o,l,h,u,d=this.strm,f=this.options.chunkSize,p=this.options.dictionary,m=!1;if(this.ended)return!1;o=t===~~t?t:!0===t?s.Z_FINISH:s.Z_NO_FLUSH,"string"==typeof e?d.input=a.binstring2buf(e):"[object ArrayBuffer]"===c.call(e)?d.input=new Uint8Array(e):d.input=e,d.next_in=0,d.avail_in=d.input.length;do{if(0===d.avail_out&&(d.output=new i.Buf8(f),d.next_out=0,d.avail_out=f),(r=n.inflate(d,s.Z_NO_FLUSH))===s.Z_NEED_DICT&&p&&(r=n.inflateSetDictionary(this.strm,p)),r===s.Z_BUF_ERROR&&!0===m&&(r=s.Z_OK,m=!1),r!==s.Z_STREAM_END&&r!==s.Z_OK)return this.onEnd(r),this.ended=!0,!1;d.next_out&&(0!==d.avail_out&&r!==s.Z_STREAM_END&&(0!==d.avail_in||o!==s.Z_FINISH&&o!==s.Z_SYNC_FLUSH)||("string"===this.options.to?(l=a.utf8border(d.output,d.next_out),h=d.next_out-l,u=a.buf2string(d.output,l),d.next_out=h,d.avail_out=f-h,h&&i.arraySet(d.output,d.output,l,h,0),this.onData(u)):this.onData(i.shrinkBuf(d.output,d.next_out)))),0===d.avail_in&&0===d.avail_out&&(m=!0)}while((d.avail_in>0||0===d.avail_out)&&r!==s.Z_STREAM_END);return r===s.Z_STREAM_END&&(o=s.Z_FINISH),o===s.Z_FINISH?(r=n.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===s.Z_OK):o!==s.Z_SYNC_FLUSH||(this.onEnd(s.Z_OK),d.avail_out=0,!0)},u.prototype.onData=function(e){this.chunks.push(e)},u.prototype.onEnd=function(e){e===s.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=i.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},t.Inflate=u,t.inflate=d,t.inflateRaw=function(e,t){return(t=t||{}).raw=!0,d(e,t)},t.ungzip=d},9805:(e,t)=>{"use strict";var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var i in r)n(r,i)&&(e[i]=r[i])}}return e},t.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var i={arraySet:function(e,t,r,n,i){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+n),i);else for(var a=0;a<n;a++)e[i+a]=t[r+a]},flattenChunks:function(e){var t,r,n,i,a,s;for(n=0,t=0,r=e.length;t<r;t++)n+=e[t].length;for(s=new Uint8Array(n),i=0,t=0,r=e.length;t<r;t++)a=e[t],s.set(a,i),i+=a.length;return s}},a={arraySet:function(e,t,r,n,i){for(var a=0;a<n;a++)e[i+a]=t[r+a]},flattenChunks:function(e){return[].concat.apply([],e)}};t.setTyped=function(e){e?(t.Buf8=Uint8Array,t.Buf16=Uint16Array,t.Buf32=Int32Array,t.assign(t,i)):(t.Buf8=Array,t.Buf16=Array,t.Buf32=Array,t.assign(t,a))},t.setTyped(r)},1996:(e,t,r)=>{"use strict";var n=r(9805),i=!0,a=!0;try{String.fromCharCode.apply(null,[0])}catch(e){i=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){a=!1}for(var s=new n.Buf8(256),o=0;o<256;o++)s[o]=o>=252?6:o>=248?5:o>=240?4:o>=224?3:o>=192?2:1;function l(e,t){if(t<65534&&(e.subarray&&a||!e.subarray&&i))return String.fromCharCode.apply(null,n.shrinkBuf(e,t));for(var r="",s=0;s<t;s++)r+=String.fromCharCode(e[s]);return r}s[254]=s[254]=1,t.string2buf=function(e){var t,r,i,a,s,o=e.length,l=0;for(a=0;a<o;a++)55296==(64512&(r=e.charCodeAt(a)))&&a+1<o&&56320==(64512&(i=e.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(i-56320),a++),l+=r<128?1:r<2048?2:r<65536?3:4;for(t=new n.Buf8(l),s=0,a=0;s<l;a++)55296==(64512&(r=e.charCodeAt(a)))&&a+1<o&&56320==(64512&(i=e.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(i-56320),a++),r<128?t[s++]=r:r<2048?(t[s++]=192|r>>>6,t[s++]=128|63&r):r<65536?(t[s++]=224|r>>>12,t[s++]=128|r>>>6&63,t[s++]=128|63&r):(t[s++]=240|r>>>18,t[s++]=128|r>>>12&63,t[s++]=128|r>>>6&63,t[s++]=128|63&r);return t},t.buf2binstring=function(e){return l(e,e.length)},t.binstring2buf=function(e){for(var t=new n.Buf8(e.length),r=0,i=t.length;r<i;r++)t[r]=e.charCodeAt(r);return t},t.buf2string=function(e,t){var r,n,i,a,o=t||e.length,h=new Array(2*o);for(n=0,r=0;r<o;)if((i=e[r++])<128)h[n++]=i;else if((a=s[i])>4)h[n++]=65533,r+=a-1;else{for(i&=2===a?31:3===a?15:7;a>1&&r<o;)i=i<<6|63&e[r++],a--;a>1?h[n++]=65533:i<65536?h[n++]=i:(i-=65536,h[n++]=55296|i>>10&1023,h[n++]=56320|1023&i)}return l(h,n)},t.utf8border=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;r>=0&&128==(192&e[r]);)r--;return r<0||0===r?t:r+s[e[r]]>t?r:t}},3269:e=>{"use strict";e.exports=function(e,t,r,n){for(var i=65535&e,a=e>>>16&65535,s=0;0!==r;){r-=s=r>2e3?2e3:r;do{a=a+(i=i+t[n++]|0)|0}while(--s);i%=65521,a%=65521}return i|a<<16}},9681:e=>{"use strict";e.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},4823:e=>{"use strict";var t=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();e.exports=function(e,r,n,i){var a=t,s=i+n;e^=-1;for(var o=i;o<s;o++)e=e>>>8^a[255&(e^r[o])];return~e}},8411:(e,t,r)=>{"use strict";var n,i=r(9805),a=r(3665),s=r(3269),o=r(4823),l=r(4674),h=0,c=0,u=-2,d=2,f=8,p=286,m=30,g=19,b=2*p+1,_=15,y=3,w=258,v=w+y+1,k=42,x=103,S=113,C=666;function E(e,t){return e.msg=l[t],t}function A(e){return(e<<1)-(e>4?9:0)}function T(e){for(var t=e.length;--t>=0;)e[t]=0}function B(e){var t=e.state,r=t.pending;r>e.avail_out&&(r=e.avail_out),0!==r&&(i.arraySet(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,0===t.pending&&(t.pending_out=0))}function N(e,t){a._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,B(e.strm)}function R(e,t){e.pending_buf[e.pending++]=t}function O(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function z(e,t){var r,n,i=e.max_chain_length,a=e.strstart,s=e.prev_length,o=e.nice_match,l=e.strstart>e.w_size-v?e.strstart-(e.w_size-v):0,h=e.window,c=e.w_mask,u=e.prev,d=e.strstart+w,f=h[a+s-1],p=h[a+s];e.prev_length>=e.good_match&&(i>>=2),o>e.lookahead&&(o=e.lookahead);do{if(h[(r=t)+s]===p&&h[r+s-1]===f&&h[r]===h[a]&&h[++r]===h[a+1]){a+=2,r++;do{}while(h[++a]===h[++r]&&h[++a]===h[++r]&&h[++a]===h[++r]&&h[++a]===h[++r]&&h[++a]===h[++r]&&h[++a]===h[++r]&&h[++a]===h[++r]&&h[++a]===h[++r]&&a<d);if(n=w-(d-a),a=d-w,n>s){if(e.match_start=t,s=n,n>=o)break;f=h[a+s-1],p=h[a+s]}}}while((t=u[t&c])>l&&0!=--i);return s<=e.lookahead?s:e.lookahead}function I(e){var t,r,n,a,l,h,c,u,d,f,p=e.w_size;do{if(a=e.window_size-e.lookahead-e.strstart,e.strstart>=p+(p-v)){i.arraySet(e.window,e.window,p,p,0),e.match_start-=p,e.strstart-=p,e.block_start-=p,t=r=e.hash_size;do{n=e.head[--t],e.head[t]=n>=p?n-p:0}while(--r);t=r=p;do{n=e.prev[--t],e.prev[t]=n>=p?n-p:0}while(--r);a+=p}if(0===e.strm.avail_in)break;if(h=e.strm,c=e.window,u=e.strstart+e.lookahead,d=a,f=void 0,(f=h.avail_in)>d&&(f=d),r=0===f?0:(h.avail_in-=f,i.arraySet(c,h.input,h.next_in,f,u),1===h.state.wrap?h.adler=s(h.adler,c,f,u):2===h.state.wrap&&(h.adler=o(h.adler,c,f,u)),h.next_in+=f,h.total_in+=f,f),e.lookahead+=r,e.lookahead+e.insert>=y)for(l=e.strstart-e.insert,e.ins_h=e.window[l],e.ins_h=(e.ins_h<<e.hash_shift^e.window[l+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[l+y-1])&e.hash_mask,e.prev[l&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=l,l++,e.insert--,!(e.lookahead+e.insert<y)););}while(e.lookahead<v&&0!==e.strm.avail_in)}function P(e,t){for(var r,n;;){if(e.lookahead<v){if(I(e),e.lookahead<v&&t===h)return 1;if(0===e.lookahead)break}if(r=0,e.lookahead>=y&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+y-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-v&&(e.match_length=z(e,r)),e.match_length>=y)if(n=a._tr_tally(e,e.strstart-e.match_start,e.match_length-y),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=y){e.match_length--;do{e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+y-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else n=a._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(n&&(N(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<y-1?e.strstart:y-1,4===t?(N(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(N(e,!1),0===e.strm.avail_out)?1:2}function D(e,t){for(var r,n,i;;){if(e.lookahead<v){if(I(e),e.lookahead<v&&t===h)return 1;if(0===e.lookahead)break}if(r=0,e.lookahead>=y&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+y-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=y-1,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-v&&(e.match_length=z(e,r),e.match_length<=5&&(1===e.strategy||e.match_length===y&&e.strstart-e.match_start>4096)&&(e.match_length=y-1)),e.prev_length>=y&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-y,n=a._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-y),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+y-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=y-1,e.strstart++,n&&(N(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if((n=a._tr_tally(e,0,e.window[e.strstart-1]))&&N(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(n=a._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<y-1?e.strstart:y-1,4===t?(N(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(N(e,!1),0===e.strm.avail_out)?1:2}function j(e,t,r,n,i){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=i}function L(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=f,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new i.Buf16(2*b),this.dyn_dtree=new i.Buf16(2*(2*m+1)),this.bl_tree=new i.Buf16(2*(2*g+1)),T(this.dyn_ltree),T(this.dyn_dtree),T(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new i.Buf16(_+1),this.heap=new i.Buf16(2*p+1),T(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new i.Buf16(2*p+1),T(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function M(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=d,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?k:S,e.adler=2===t.wrap?0:1,t.last_flush=h,a._tr_init(t),c):E(e,u)}function F(e){var t,r=M(e);return r===c&&((t=e.state).window_size=2*t.w_size,T(t.head),t.max_lazy_match=n[t.level].max_lazy,t.good_match=n[t.level].good_length,t.nice_match=n[t.level].nice_length,t.max_chain_length=n[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=y-1,t.match_available=0,t.ins_h=0),r}function U(e,t,r,n,a,s){if(!e)return u;var o=1;if(-1===t&&(t=6),n<0?(o=0,n=-n):n>15&&(o=2,n-=16),a<1||a>9||r!==f||n<8||n>15||t<0||t>9||s<0||s>4)return E(e,u);8===n&&(n=9);var l=new L;return e.state=l,l.strm=e,l.wrap=o,l.gzhead=null,l.w_bits=n,l.w_size=1<<l.w_bits,l.w_mask=l.w_size-1,l.hash_bits=a+7,l.hash_size=1<<l.hash_bits,l.hash_mask=l.hash_size-1,l.hash_shift=~~((l.hash_bits+y-1)/y),l.window=new i.Buf8(2*l.w_size),l.head=new i.Buf16(l.hash_size),l.prev=new i.Buf16(l.w_size),l.lit_bufsize=1<<a+6,l.pending_buf_size=4*l.lit_bufsize,l.pending_buf=new i.Buf8(l.pending_buf_size),l.d_buf=1*l.lit_bufsize,l.l_buf=3*l.lit_bufsize,l.level=t,l.strategy=s,l.method=r,F(e)}n=[new j(0,0,0,0,(function(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(I(e),0===e.lookahead&&t===h)return 1;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,N(e,!1),0===e.strm.avail_out))return 1;if(e.strstart-e.block_start>=e.w_size-v&&(N(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(N(e,!0),0===e.strm.avail_out?3:4):(e.strstart>e.block_start&&(N(e,!1),e.strm.avail_out),1)})),new j(4,4,8,4,P),new j(4,5,16,8,P),new j(4,6,32,32,P),new j(4,4,16,16,D),new j(8,16,32,32,D),new j(8,16,128,128,D),new j(8,32,128,256,D),new j(32,128,258,1024,D),new j(32,258,258,4096,D)],t.deflateInit=function(e,t){return U(e,t,f,15,8,0)},t.deflateInit2=U,t.deflateReset=F,t.deflateResetKeep=M,t.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?u:(e.state.gzhead=t,c):u},t.deflate=function(e,t){var r,i,s,l;if(!e||!e.state||t>5||t<0)return e?E(e,u):u;if(i=e.state,!e.output||!e.input&&0!==e.avail_in||i.status===C&&4!==t)return E(e,0===e.avail_out?-5:u);if(i.strm=e,r=i.last_flush,i.last_flush=t,i.status===k)if(2===i.wrap)e.adler=0,R(i,31),R(i,139),R(i,8),i.gzhead?(R(i,(i.gzhead.text?1:0)+(i.gzhead.hcrc?2:0)+(i.gzhead.extra?4:0)+(i.gzhead.name?8:0)+(i.gzhead.comment?16:0)),R(i,255&i.gzhead.time),R(i,i.gzhead.time>>8&255),R(i,i.gzhead.time>>16&255),R(i,i.gzhead.time>>24&255),R(i,9===i.level?2:i.strategy>=2||i.level<2?4:0),R(i,255&i.gzhead.os),i.gzhead.extra&&i.gzhead.extra.length&&(R(i,255&i.gzhead.extra.length),R(i,i.gzhead.extra.length>>8&255)),i.gzhead.hcrc&&(e.adler=o(e.adler,i.pending_buf,i.pending,0)),i.gzindex=0,i.status=69):(R(i,0),R(i,0),R(i,0),R(i,0),R(i,0),R(i,9===i.level?2:i.strategy>=2||i.level<2?4:0),R(i,3),i.status=S);else{var d=f+(i.w_bits-8<<4)<<8;d|=(i.strategy>=2||i.level<2?0:i.level<6?1:6===i.level?2:3)<<6,0!==i.strstart&&(d|=32),d+=31-d%31,i.status=S,O(i,d),0!==i.strstart&&(O(i,e.adler>>>16),O(i,65535&e.adler)),e.adler=1}if(69===i.status)if(i.gzhead.extra){for(s=i.pending;i.gzindex<(65535&i.gzhead.extra.length)&&(i.pending!==i.pending_buf_size||(i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),B(e),s=i.pending,i.pending!==i.pending_buf_size));)R(i,255&i.gzhead.extra[i.gzindex]),i.gzindex++;i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),i.gzindex===i.gzhead.extra.length&&(i.gzindex=0,i.status=73)}else i.status=73;if(73===i.status)if(i.gzhead.name){s=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),B(e),s=i.pending,i.pending===i.pending_buf_size)){l=1;break}l=i.gzindex<i.gzhead.name.length?255&i.gzhead.name.charCodeAt(i.gzindex++):0,R(i,l)}while(0!==l);i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),0===l&&(i.gzindex=0,i.status=91)}else i.status=91;if(91===i.status)if(i.gzhead.comment){s=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),B(e),s=i.pending,i.pending===i.pending_buf_size)){l=1;break}l=i.gzindex<i.gzhead.comment.length?255&i.gzhead.comment.charCodeAt(i.gzindex++):0,R(i,l)}while(0!==l);i.gzhead.hcrc&&i.pending>s&&(e.adler=o(e.adler,i.pending_buf,i.pending-s,s)),0===l&&(i.status=x)}else i.status=x;if(i.status===x&&(i.gzhead.hcrc?(i.pending+2>i.pending_buf_size&&B(e),i.pending+2<=i.pending_buf_size&&(R(i,255&e.adler),R(i,e.adler>>8&255),e.adler=0,i.status=S)):i.status=S),0!==i.pending){if(B(e),0===e.avail_out)return i.last_flush=-1,c}else if(0===e.avail_in&&A(t)<=A(r)&&4!==t)return E(e,-5);if(i.status===C&&0!==e.avail_in)return E(e,-5);if(0!==e.avail_in||0!==i.lookahead||t!==h&&i.status!==C){var p=2===i.strategy?function(e,t){for(var r;;){if(0===e.lookahead&&(I(e),0===e.lookahead)){if(t===h)return 1;break}if(e.match_length=0,r=a._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(N(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(N(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(N(e,!1),0===e.strm.avail_out)?1:2}(i,t):3===i.strategy?function(e,t){for(var r,n,i,s,o=e.window;;){if(e.lookahead<=w){if(I(e),e.lookahead<=w&&t===h)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=y&&e.strstart>0&&(n=o[i=e.strstart-1])===o[++i]&&n===o[++i]&&n===o[++i]){s=e.strstart+w;do{}while(n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&i<s);e.match_length=w-(s-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=y?(r=a._tr_tally(e,1,e.match_length-y),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=a._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(N(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,4===t?(N(e,!0),0===e.strm.avail_out?3:4):e.last_lit&&(N(e,!1),0===e.strm.avail_out)?1:2}(i,t):n[i.level].func(i,t);if(3!==p&&4!==p||(i.status=C),1===p||3===p)return 0===e.avail_out&&(i.last_flush=-1),c;if(2===p&&(1===t?a._tr_align(i):5!==t&&(a._tr_stored_block(i,0,0,!1),3===t&&(T(i.head),0===i.lookahead&&(i.strstart=0,i.block_start=0,i.insert=0))),B(e),0===e.avail_out))return i.last_flush=-1,c}return 4!==t?c:i.wrap<=0?1:(2===i.wrap?(R(i,255&e.adler),R(i,e.adler>>8&255),R(i,e.adler>>16&255),R(i,e.adler>>24&255),R(i,255&e.total_in),R(i,e.total_in>>8&255),R(i,e.total_in>>16&255),R(i,e.total_in>>24&255)):(O(i,e.adler>>>16),O(i,65535&e.adler)),B(e),i.wrap>0&&(i.wrap=-i.wrap),0!==i.pending?c:1)},t.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==k&&69!==t&&73!==t&&91!==t&&t!==x&&t!==S&&t!==C?E(e,u):(e.state=null,t===S?E(e,-3):c):u},t.deflateSetDictionary=function(e,t){var r,n,a,o,l,h,d,f,p=t.length;if(!e||!e.state)return u;if(2===(o=(r=e.state).wrap)||1===o&&r.status!==k||r.lookahead)return u;for(1===o&&(e.adler=s(e.adler,t,p,0)),r.wrap=0,p>=r.w_size&&(0===o&&(T(r.head),r.strstart=0,r.block_start=0,r.insert=0),f=new i.Buf8(r.w_size),i.arraySet(f,t,p-r.w_size,r.w_size,0),t=f,p=r.w_size),l=e.avail_in,h=e.next_in,d=e.input,e.avail_in=p,e.next_in=0,e.input=t,I(r);r.lookahead>=y;){n=r.strstart,a=r.lookahead-(y-1);do{r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+y-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++}while(--a);r.strstart=n,r.lookahead=y-1,I(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=y-1,r.match_available=0,e.next_in=h,e.input=d,e.avail_in=l,r.wrap=o,c},t.deflateInfo="pako deflate (from Nodeca project)"},7414:e=>{"use strict";e.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},7293:e=>{"use strict";e.exports=function(e,t){var r,n,i,a,s,o,l,h,c,u,d,f,p,m,g,b,_,y,w,v,k,x,S,C,E;r=e.state,n=e.next_in,C=e.input,i=n+(e.avail_in-5),a=e.next_out,E=e.output,s=a-(t-e.avail_out),o=a+(e.avail_out-257),l=r.dmax,h=r.wsize,c=r.whave,u=r.wnext,d=r.window,f=r.hold,p=r.bits,m=r.lencode,g=r.distcode,b=(1<<r.lenbits)-1,_=(1<<r.distbits)-1;e:do{p<15&&(f+=C[n++]<<p,p+=8,f+=C[n++]<<p,p+=8),y=m[f&b];t:for(;;){if(f>>>=w=y>>>24,p-=w,0==(w=y>>>16&255))E[a++]=65535&y;else{if(!(16&w)){if(64&w){if(32&w){r.mode=12;break e}e.msg="invalid literal/length code",r.mode=30;break e}y=m[(65535&y)+(f&(1<<w)-1)];continue t}for(v=65535&y,(w&=15)&&(p<w&&(f+=C[n++]<<p,p+=8),v+=f&(1<<w)-1,f>>>=w,p-=w),p<15&&(f+=C[n++]<<p,p+=8,f+=C[n++]<<p,p+=8),y=g[f&_];;){if(f>>>=w=y>>>24,p-=w,16&(w=y>>>16&255)){if(k=65535&y,p<(w&=15)&&(f+=C[n++]<<p,(p+=8)<w&&(f+=C[n++]<<p,p+=8)),(k+=f&(1<<w)-1)>l){e.msg="invalid distance too far back",r.mode=30;break e}if(f>>>=w,p-=w,k>(w=a-s)){if((w=k-w)>c&&r.sane){e.msg="invalid distance too far back",r.mode=30;break e}if(x=0,S=d,0===u){if(x+=h-w,w<v){v-=w;do{E[a++]=d[x++]}while(--w);x=a-k,S=E}}else if(u<w){if(x+=h+u-w,(w-=u)<v){v-=w;do{E[a++]=d[x++]}while(--w);if(x=0,u<v){v-=w=u;do{E[a++]=d[x++]}while(--w);x=a-k,S=E}}}else if(x+=u-w,w<v){v-=w;do{E[a++]=d[x++]}while(--w);x=a-k,S=E}for(;v>2;)E[a++]=S[x++],E[a++]=S[x++],E[a++]=S[x++],v-=3;v&&(E[a++]=S[x++],v>1&&(E[a++]=S[x++]))}else{x=a-k;do{E[a++]=E[x++],E[a++]=E[x++],E[a++]=E[x++],v-=3}while(v>2);v&&(E[a++]=E[x++],v>1&&(E[a++]=E[x++]))}break}if(64&w){e.msg="invalid distance code",r.mode=30;break e}y=g[(65535&y)+(f&(1<<w)-1)]}}break}}while(n<i&&a<o);n-=v=p>>3,f&=(1<<(p-=v<<3))-1,e.next_in=n,e.next_out=a,e.avail_in=n<i?i-n+5:5-(n-i),e.avail_out=a<o?o-a+257:257-(a-o),r.hold=f,r.bits=p}},1447:(e,t,r)=>{"use strict";var n=r(9805),i=r(3269),a=r(4823),s=r(7293),o=r(1998),l=0,h=-2,c=1,u=12,d=30,f=852,p=592;function m(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function g(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new n.Buf16(320),this.work=new n.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function b(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=c,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new n.Buf32(f),t.distcode=t.distdyn=new n.Buf32(p),t.sane=1,t.back=-1,l):h}function _(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,b(e)):h}function y(e,t){var r,n;return e&&e.state?(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?h:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,_(e))):h}function w(e,t){var r,n;return e?(n=new g,e.state=n,n.window=null,(r=y(e,t))!==l&&(e.state=null),r):h}var v,k,x=!0;function S(e){if(x){var t;for(v=new n.Buf32(512),k=new n.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(o(1,e.lens,0,288,v,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;o(2,e.lens,0,32,k,0,e.work,{bits:5}),x=!1}e.lencode=v,e.lenbits=9,e.distcode=k,e.distbits=5}function C(e,t,r,i){var a,s=e.state;return null===s.window&&(s.wsize=1<<s.wbits,s.wnext=0,s.whave=0,s.window=new n.Buf8(s.wsize)),i>=s.wsize?(n.arraySet(s.window,t,r-s.wsize,s.wsize,0),s.wnext=0,s.whave=s.wsize):((a=s.wsize-s.wnext)>i&&(a=i),n.arraySet(s.window,t,r-i,a,s.wnext),(i-=a)?(n.arraySet(s.window,t,r-i,i,0),s.wnext=i,s.whave=s.wsize):(s.wnext+=a,s.wnext===s.wsize&&(s.wnext=0),s.whave<s.wsize&&(s.whave+=a))),0}t.inflateReset=_,t.inflateReset2=y,t.inflateResetKeep=b,t.inflateInit=function(e){return w(e,15)},t.inflateInit2=w,t.inflate=function(e,t){var r,f,p,g,b,_,y,w,v,k,x,E,A,T,B,N,R,O,z,I,P,D,j,L,M=0,F=new n.Buf8(4),U=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return h;(r=e.state).mode===u&&(r.mode=13),b=e.next_out,p=e.output,y=e.avail_out,g=e.next_in,f=e.input,_=e.avail_in,w=r.hold,v=r.bits,k=_,x=y,D=l;e:for(;;)switch(r.mode){case c:if(0===r.wrap){r.mode=13;break}for(;v<16;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}if(2&r.wrap&&35615===w){r.check=0,F[0]=255&w,F[1]=w>>>8&255,r.check=a(r.check,F,2,0),w=0,v=0,r.mode=2;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&w)<<8)+(w>>8))%31){e.msg="incorrect header check",r.mode=d;break}if(8!=(15&w)){e.msg="unknown compression method",r.mode=d;break}if(v-=4,P=8+(15&(w>>>=4)),0===r.wbits)r.wbits=P;else if(P>r.wbits){e.msg="invalid window size",r.mode=d;break}r.dmax=1<<P,e.adler=r.check=1,r.mode=512&w?10:u,w=0,v=0;break;case 2:for(;v<16;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}if(r.flags=w,8!=(255&r.flags)){e.msg="unknown compression method",r.mode=d;break}if(57344&r.flags){e.msg="unknown header flags set",r.mode=d;break}r.head&&(r.head.text=w>>8&1),512&r.flags&&(F[0]=255&w,F[1]=w>>>8&255,r.check=a(r.check,F,2,0)),w=0,v=0,r.mode=3;case 3:for(;v<32;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}r.head&&(r.head.time=w),512&r.flags&&(F[0]=255&w,F[1]=w>>>8&255,F[2]=w>>>16&255,F[3]=w>>>24&255,r.check=a(r.check,F,4,0)),w=0,v=0,r.mode=4;case 4:for(;v<16;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}r.head&&(r.head.xflags=255&w,r.head.os=w>>8),512&r.flags&&(F[0]=255&w,F[1]=w>>>8&255,r.check=a(r.check,F,2,0)),w=0,v=0,r.mode=5;case 5:if(1024&r.flags){for(;v<16;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}r.length=w,r.head&&(r.head.extra_len=w),512&r.flags&&(F[0]=255&w,F[1]=w>>>8&255,r.check=a(r.check,F,2,0)),w=0,v=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&((E=r.length)>_&&(E=_),E&&(r.head&&(P=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),n.arraySet(r.head.extra,f,g,E,P)),512&r.flags&&(r.check=a(r.check,f,E,g)),_-=E,g+=E,r.length-=E),r.length))break e;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===_)break e;E=0;do{P=f[g+E++],r.head&&P&&r.length<65536&&(r.head.name+=String.fromCharCode(P))}while(P&&E<_);if(512&r.flags&&(r.check=a(r.check,f,E,g)),_-=E,g+=E,P)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===_)break e;E=0;do{P=f[g+E++],r.head&&P&&r.length<65536&&(r.head.comment+=String.fromCharCode(P))}while(P&&E<_);if(512&r.flags&&(r.check=a(r.check,f,E,g)),_-=E,g+=E,P)break e}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;v<16;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}if(w!==(65535&r.check)){e.msg="header crc mismatch",r.mode=d;break}w=0,v=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=u;break;case 10:for(;v<32;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}e.adler=r.check=m(w),w=0,v=0,r.mode=11;case 11:if(0===r.havedict)return e.next_out=b,e.avail_out=y,e.next_in=g,e.avail_in=_,r.hold=w,r.bits=v,2;e.adler=r.check=1,r.mode=u;case u:if(5===t||6===t)break e;case 13:if(r.last){w>>>=7&v,v-=7&v,r.mode=27;break}for(;v<3;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}switch(r.last=1&w,v-=1,3&(w>>>=1)){case 0:r.mode=14;break;case 1:if(S(r),r.mode=20,6===t){w>>>=2,v-=2;break e}break;case 2:r.mode=17;break;case 3:e.msg="invalid block type",r.mode=d}w>>>=2,v-=2;break;case 14:for(w>>>=7&v,v-=7&v;v<32;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}if((65535&w)!=(w>>>16^65535)){e.msg="invalid stored block lengths",r.mode=d;break}if(r.length=65535&w,w=0,v=0,r.mode=15,6===t)break e;case 15:r.mode=16;case 16:if(E=r.length){if(E>_&&(E=_),E>y&&(E=y),0===E)break e;n.arraySet(p,f,g,E,b),_-=E,g+=E,y-=E,b+=E,r.length-=E;break}r.mode=u;break;case 17:for(;v<14;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}if(r.nlen=257+(31&w),w>>>=5,v-=5,r.ndist=1+(31&w),w>>>=5,v-=5,r.ncode=4+(15&w),w>>>=4,v-=4,r.nlen>286||r.ndist>30){e.msg="too many length or distance symbols",r.mode=d;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;v<3;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}r.lens[U[r.have++]]=7&w,w>>>=3,v-=3}for(;r.have<19;)r.lens[U[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,j={bits:r.lenbits},D=o(0,r.lens,0,19,r.lencode,0,r.work,j),r.lenbits=j.bits,D){e.msg="invalid code lengths set",r.mode=d;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;N=(M=r.lencode[w&(1<<r.lenbits)-1])>>>16&255,R=65535&M,!((B=M>>>24)<=v);){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}if(R<16)w>>>=B,v-=B,r.lens[r.have++]=R;else{if(16===R){for(L=B+2;v<L;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}if(w>>>=B,v-=B,0===r.have){e.msg="invalid bit length repeat",r.mode=d;break}P=r.lens[r.have-1],E=3+(3&w),w>>>=2,v-=2}else if(17===R){for(L=B+3;v<L;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}v-=B,P=0,E=3+(7&(w>>>=B)),w>>>=3,v-=3}else{for(L=B+7;v<L;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}v-=B,P=0,E=11+(127&(w>>>=B)),w>>>=7,v-=7}if(r.have+E>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=d;break}for(;E--;)r.lens[r.have++]=P}}if(r.mode===d)break;if(0===r.lens[256]){e.msg="invalid code -- missing end-of-block",r.mode=d;break}if(r.lenbits=9,j={bits:r.lenbits},D=o(1,r.lens,0,r.nlen,r.lencode,0,r.work,j),r.lenbits=j.bits,D){e.msg="invalid literal/lengths set",r.mode=d;break}if(r.distbits=6,r.distcode=r.distdyn,j={bits:r.distbits},D=o(2,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,j),r.distbits=j.bits,D){e.msg="invalid distances set",r.mode=d;break}if(r.mode=20,6===t)break e;case 20:r.mode=21;case 21:if(_>=6&&y>=258){e.next_out=b,e.avail_out=y,e.next_in=g,e.avail_in=_,r.hold=w,r.bits=v,s(e,x),b=e.next_out,p=e.output,y=e.avail_out,g=e.next_in,f=e.input,_=e.avail_in,w=r.hold,v=r.bits,r.mode===u&&(r.back=-1);break}for(r.back=0;N=(M=r.lencode[w&(1<<r.lenbits)-1])>>>16&255,R=65535&M,!((B=M>>>24)<=v);){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}if(N&&!(240&N)){for(O=B,z=N,I=R;N=(M=r.lencode[I+((w&(1<<O+z)-1)>>O)])>>>16&255,R=65535&M,!(O+(B=M>>>24)<=v);){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}w>>>=O,v-=O,r.back+=O}if(w>>>=B,v-=B,r.back+=B,r.length=R,0===N){r.mode=26;break}if(32&N){r.back=-1,r.mode=u;break}if(64&N){e.msg="invalid literal/length code",r.mode=d;break}r.extra=15&N,r.mode=22;case 22:if(r.extra){for(L=r.extra;v<L;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}r.length+=w&(1<<r.extra)-1,w>>>=r.extra,v-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;N=(M=r.distcode[w&(1<<r.distbits)-1])>>>16&255,R=65535&M,!((B=M>>>24)<=v);){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}if(!(240&N)){for(O=B,z=N,I=R;N=(M=r.distcode[I+((w&(1<<O+z)-1)>>O)])>>>16&255,R=65535&M,!(O+(B=M>>>24)<=v);){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}w>>>=O,v-=O,r.back+=O}if(w>>>=B,v-=B,r.back+=B,64&N){e.msg="invalid distance code",r.mode=d;break}r.offset=R,r.extra=15&N,r.mode=24;case 24:if(r.extra){for(L=r.extra;v<L;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}r.offset+=w&(1<<r.extra)-1,w>>>=r.extra,v-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=d;break}r.mode=25;case 25:if(0===y)break e;if(E=x-y,r.offset>E){if((E=r.offset-E)>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=d;break}E>r.wnext?(E-=r.wnext,A=r.wsize-E):A=r.wnext-E,E>r.length&&(E=r.length),T=r.window}else T=p,A=b-r.offset,E=r.length;E>y&&(E=y),y-=E,r.length-=E;do{p[b++]=T[A++]}while(--E);0===r.length&&(r.mode=21);break;case 26:if(0===y)break e;p[b++]=r.length,y--,r.mode=21;break;case 27:if(r.wrap){for(;v<32;){if(0===_)break e;_--,w|=f[g++]<<v,v+=8}if(x-=y,e.total_out+=x,r.total+=x,x&&(e.adler=r.check=r.flags?a(r.check,p,x,b-x):i(r.check,p,x,b-x)),x=y,(r.flags?w:m(w))!==r.check){e.msg="incorrect data check",r.mode=d;break}w=0,v=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;v<32;){if(0===_)break e;_--,w+=f[g++]<<v,v+=8}if(w!==(4294967295&r.total)){e.msg="incorrect length check",r.mode=d;break}w=0,v=0}r.mode=29;case 29:D=1;break e;case d:D=-3;break e;case 31:return-4;default:return h}return e.next_out=b,e.avail_out=y,e.next_in=g,e.avail_in=_,r.hold=w,r.bits=v,(r.wsize||x!==e.avail_out&&r.mode<d&&(r.mode<27||4!==t))&&C(e,e.output,e.next_out,x-e.avail_out)?(r.mode=31,-4):(k-=e.avail_in,x-=e.avail_out,e.total_in+=k,e.total_out+=x,r.total+=x,r.wrap&&x&&(e.adler=r.check=r.flags?a(r.check,p,x,e.next_out-x):i(r.check,p,x,e.next_out-x)),e.data_type=r.bits+(r.last?64:0)+(r.mode===u?128:0)+(20===r.mode||15===r.mode?256:0),(0===k&&0===x||4===t)&&D===l&&(D=-5),D)},t.inflateEnd=function(e){if(!e||!e.state)return h;var t=e.state;return t.window&&(t.window=null),e.state=null,l},t.inflateGetHeader=function(e,t){var r;return e&&e.state&&2&(r=e.state).wrap?(r.head=t,t.done=!1,l):h},t.inflateSetDictionary=function(e,t){var r,n=t.length;return e&&e.state?0!==(r=e.state).wrap&&11!==r.mode?h:11===r.mode&&i(1,t,n,0)!==r.check?-3:C(e,t,n,n)?(r.mode=31,-4):(r.havedict=1,l):h},t.inflateInfo="pako inflate (from Nodeca project)"},1998:(e,t,r)=>{"use strict";var n=r(9805),i=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],a=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],s=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],o=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];e.exports=function(e,t,r,l,h,c,u,d){var f,p,m,g,b,_,y,w,v,k=d.bits,x=0,S=0,C=0,E=0,A=0,T=0,B=0,N=0,R=0,O=0,z=null,I=0,P=new n.Buf16(16),D=new n.Buf16(16),j=null,L=0;for(x=0;x<=15;x++)P[x]=0;for(S=0;S<l;S++)P[t[r+S]]++;for(A=k,E=15;E>=1&&0===P[E];E--);if(A>E&&(A=E),0===E)return h[c++]=20971520,h[c++]=20971520,d.bits=1,0;for(C=1;C<E&&0===P[C];C++);for(A<C&&(A=C),N=1,x=1;x<=15;x++)if(N<<=1,(N-=P[x])<0)return-1;if(N>0&&(0===e||1!==E))return-1;for(D[1]=0,x=1;x<15;x++)D[x+1]=D[x]+P[x];for(S=0;S<l;S++)0!==t[r+S]&&(u[D[t[r+S]]++]=S);if(0===e?(z=j=u,_=19):1===e?(z=i,I-=257,j=a,L-=257,_=256):(z=s,j=o,_=-1),O=0,S=0,x=C,b=c,T=A,B=0,m=-1,g=(R=1<<A)-1,1===e&&R>852||2===e&&R>592)return 1;for(;;){y=x-B,u[S]<_?(w=0,v=u[S]):u[S]>_?(w=j[L+u[S]],v=z[I+u[S]]):(w=96,v=0),f=1<<x-B,C=p=1<<T;do{h[b+(O>>B)+(p-=f)]=y<<24|w<<16|v}while(0!==p);for(f=1<<x-1;O&f;)f>>=1;if(0!==f?(O&=f-1,O+=f):O=0,S++,0==--P[x]){if(x===E)break;x=t[r+u[S]]}if(x>A&&(O&g)!==m){for(0===B&&(B=A),b+=C,N=1<<(T=x-B);T+B<E&&!((N-=P[T+B])<=0);)T++,N<<=1;if(R+=1<<T,1===e&&R>852||2===e&&R>592)return 1;h[m=O&g]=A<<24|T<<16|b-c}}return 0!==O&&(h[b+O]=x-B<<24|64<<16),d.bits=A,0}},4674:e=>{"use strict";e.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},3665:(e,t,r)=>{"use strict";var n=r(9805);function i(e){for(var t=e.length;--t>=0;)e[t]=0}var a=256,s=286,o=30,l=15,h=16,c=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],u=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],d=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],f=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],p=new Array(576);i(p);var m=new Array(60);i(m);var g=new Array(512);i(g);var b=new Array(256);i(b);var _=new Array(29);i(_);var y,w,v,k=new Array(o);function x(e,t,r,n,i){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=e&&e.length}function S(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function C(e){return e<256?g[e]:g[256+(e>>>7)]}function E(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function A(e,t,r){e.bi_valid>h-r?(e.bi_buf|=t<<e.bi_valid&65535,E(e,e.bi_buf),e.bi_buf=t>>h-e.bi_valid,e.bi_valid+=r-h):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function T(e,t,r){A(e,r[2*t],r[2*t+1])}function B(e,t){var r=0;do{r|=1&e,e>>>=1,r<<=1}while(--t>0);return r>>>1}function N(e,t,r){var n,i,a=new Array(l+1),s=0;for(n=1;n<=l;n++)a[n]=s=s+r[n-1]<<1;for(i=0;i<=t;i++){var o=e[2*i+1];0!==o&&(e[2*i]=B(a[o]++,o))}}function R(e){var t;for(t=0;t<s;t++)e.dyn_ltree[2*t]=0;for(t=0;t<o;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function O(e){e.bi_valid>8?E(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function z(e,t,r,n){var i=2*t,a=2*r;return e[i]<e[a]||e[i]===e[a]&&n[t]<=n[r]}function I(e,t,r){for(var n=e.heap[r],i=r<<1;i<=e.heap_len&&(i<e.heap_len&&z(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!z(t,n,e.heap[i],e.depth));)e.heap[r]=e.heap[i],r=i,i<<=1;e.heap[r]=n}function P(e,t,r){var n,i,s,o,l=0;if(0!==e.last_lit)do{n=e.pending_buf[e.d_buf+2*l]<<8|e.pending_buf[e.d_buf+2*l+1],i=e.pending_buf[e.l_buf+l],l++,0===n?T(e,i,t):(T(e,(s=b[i])+a+1,t),0!==(o=c[s])&&A(e,i-=_[s],o),T(e,s=C(--n),r),0!==(o=u[s])&&A(e,n-=k[s],o))}while(l<e.last_lit);T(e,256,t)}function D(e,t){var r,n,i,a=t.dyn_tree,s=t.stat_desc.static_tree,o=t.stat_desc.has_stree,h=t.stat_desc.elems,c=-1;for(e.heap_len=0,e.heap_max=573,r=0;r<h;r++)0!==a[2*r]?(e.heap[++e.heap_len]=c=r,e.depth[r]=0):a[2*r+1]=0;for(;e.heap_len<2;)a[2*(i=e.heap[++e.heap_len]=c<2?++c:0)]=1,e.depth[i]=0,e.opt_len--,o&&(e.static_len-=s[2*i+1]);for(t.max_code=c,r=e.heap_len>>1;r>=1;r--)I(e,a,r);i=h;do{r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],I(e,a,1),n=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=n,a[2*i]=a[2*r]+a[2*n],e.depth[i]=(e.depth[r]>=e.depth[n]?e.depth[r]:e.depth[n])+1,a[2*r+1]=a[2*n+1]=i,e.heap[1]=i++,I(e,a,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var r,n,i,a,s,o,h=t.dyn_tree,c=t.max_code,u=t.stat_desc.static_tree,d=t.stat_desc.has_stree,f=t.stat_desc.extra_bits,p=t.stat_desc.extra_base,m=t.stat_desc.max_length,g=0;for(a=0;a<=l;a++)e.bl_count[a]=0;for(h[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<573;r++)(a=h[2*h[2*(n=e.heap[r])+1]+1]+1)>m&&(a=m,g++),h[2*n+1]=a,n>c||(e.bl_count[a]++,s=0,n>=p&&(s=f[n-p]),o=h[2*n],e.opt_len+=o*(a+s),d&&(e.static_len+=o*(u[2*n+1]+s)));if(0!==g){do{for(a=m-1;0===e.bl_count[a];)a--;e.bl_count[a]--,e.bl_count[a+1]+=2,e.bl_count[m]--,g-=2}while(g>0);for(a=m;0!==a;a--)for(n=e.bl_count[a];0!==n;)(i=e.heap[--r])>c||(h[2*i+1]!==a&&(e.opt_len+=(a-h[2*i+1])*h[2*i],h[2*i+1]=a),n--)}}(e,t),N(a,c,e.bl_count)}function j(e,t,r){var n,i,a=-1,s=t[1],o=0,l=7,h=4;for(0===s&&(l=138,h=3),t[2*(r+1)+1]=65535,n=0;n<=r;n++)i=s,s=t[2*(n+1)+1],++o<l&&i===s||(o<h?e.bl_tree[2*i]+=o:0!==i?(i!==a&&e.bl_tree[2*i]++,e.bl_tree[32]++):o<=10?e.bl_tree[34]++:e.bl_tree[36]++,o=0,a=i,0===s?(l=138,h=3):i===s?(l=6,h=3):(l=7,h=4))}function L(e,t,r){var n,i,a=-1,s=t[1],o=0,l=7,h=4;for(0===s&&(l=138,h=3),n=0;n<=r;n++)if(i=s,s=t[2*(n+1)+1],!(++o<l&&i===s)){if(o<h)do{T(e,i,e.bl_tree)}while(0!=--o);else 0!==i?(i!==a&&(T(e,i,e.bl_tree),o--),T(e,16,e.bl_tree),A(e,o-3,2)):o<=10?(T(e,17,e.bl_tree),A(e,o-3,3)):(T(e,18,e.bl_tree),A(e,o-11,7));o=0,a=i,0===s?(l=138,h=3):i===s?(l=6,h=3):(l=7,h=4)}}i(k);var M=!1;function F(e,t,r,i){A(e,0+(i?1:0),3),function(e,t,r,i){O(e),E(e,r),E(e,~r),n.arraySet(e.pending_buf,e.window,t,r,e.pending),e.pending+=r}(e,t,r)}t._tr_init=function(e){M||(function(){var e,t,r,n,i,a=new Array(l+1);for(r=0,n=0;n<28;n++)for(_[n]=r,e=0;e<1<<c[n];e++)b[r++]=n;for(b[r-1]=n,i=0,n=0;n<16;n++)for(k[n]=i,e=0;e<1<<u[n];e++)g[i++]=n;for(i>>=7;n<o;n++)for(k[n]=i<<7,e=0;e<1<<u[n]-7;e++)g[256+i++]=n;for(t=0;t<=l;t++)a[t]=0;for(e=0;e<=143;)p[2*e+1]=8,e++,a[8]++;for(;e<=255;)p[2*e+1]=9,e++,a[9]++;for(;e<=279;)p[2*e+1]=7,e++,a[7]++;for(;e<=287;)p[2*e+1]=8,e++,a[8]++;for(N(p,287,a),e=0;e<o;e++)m[2*e+1]=5,m[2*e]=B(e,5);y=new x(p,c,257,s,l),w=new x(m,u,0,o,l),v=new x(new Array(0),d,0,19,7)}(),M=!0),e.l_desc=new S(e.dyn_ltree,y),e.d_desc=new S(e.dyn_dtree,w),e.bl_desc=new S(e.bl_tree,v),e.bi_buf=0,e.bi_valid=0,R(e)},t._tr_stored_block=F,t._tr_flush_block=function(e,t,r,n){var i,s,o=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,r=4093624447;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<a;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0}(e)),D(e,e.l_desc),D(e,e.d_desc),o=function(e){var t;for(j(e,e.dyn_ltree,e.l_desc.max_code),j(e,e.dyn_dtree,e.d_desc.max_code),D(e,e.bl_desc),t=18;t>=3&&0===e.bl_tree[2*f[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(s=e.static_len+3+7>>>3)<=i&&(i=s)):i=s=r+5,r+4<=i&&-1!==t?F(e,t,r,n):4===e.strategy||s===i?(A(e,2+(n?1:0),3),P(e,p,m)):(A(e,4+(n?1:0),3),function(e,t,r,n){var i;for(A(e,t-257,5),A(e,r-1,5),A(e,n-4,4),i=0;i<n;i++)A(e,e.bl_tree[2*f[i]+1],3);L(e,e.dyn_ltree,t-1),L(e,e.dyn_dtree,r-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,o+1),P(e,e.dyn_ltree,e.dyn_dtree)),R(e),n&&O(e)},t._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(b[r]+a+1)]++,e.dyn_dtree[2*C(t)]++),e.last_lit===e.lit_bufsize-1},t._tr_align=function(e){A(e,2,3),T(e,256,p),function(e){16===e.bi_valid?(E(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)}},4442:e=>{"use strict";e.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},3225:e=>{"use strict";"undefined"==typeof process||!process.version||0===process.version.indexOf("v0.")||0===process.version.indexOf("v1.")&&0!==process.version.indexOf("v1.8.")?e.exports={nextTick:function(e,t,r,n){if("function"!=typeof e)throw new TypeError('"callback" argument must be a function');var i,a,s=arguments.length;switch(s){case 0:case 1:return process.nextTick(e);case 2:return process.nextTick((function(){e.call(null,t)}));case 3:return process.nextTick((function(){e.call(null,t,r)}));case 4:return process.nextTick((function(){e.call(null,t,r,n)}));default:for(i=new Array(s-1),a=0;a<i.length;)i[a++]=arguments[a];return process.nextTick((function(){e.apply(null,i)}))}}}:e.exports=process},5382:(e,t,r)=>{"use strict";var n=r(3225),i=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};e.exports=u;var a=Object.create(r(5622));a.inherits=r(2017);var s=r(5412),o=r(6708);a.inherits(u,s);for(var l=i(o.prototype),h=0;h<l.length;h++){var c=l[h];u.prototype[c]||(u.prototype[c]=o.prototype[c])}function u(e){if(!(this instanceof u))return new u(e);s.call(this,e),o.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",d)}function d(){this.allowHalfOpen||this._writableState.ended||n.nextTick(f,this)}function f(e){e.end()}Object.defineProperty(u.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(u.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&this._readableState.destroyed&&this._writableState.destroyed},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),u.prototype._destroy=function(e,t){this.push(null),this.end(),n.nextTick(t,e)}},3600:(e,t,r)=>{"use strict";e.exports=a;var n=r(4610),i=Object.create(r(5622));function a(e){if(!(this instanceof a))return new a(e);n.call(this,e)}i.inherits=r(2017),i.inherits(a,n),a.prototype._transform=function(e,t,r){r(null,e)}},5412:(e,t,r)=>{"use strict";var n=r(3225);e.exports=_;var i,a=r(4634);_.ReadableState=b,r(4434).EventEmitter;var s=function(e,t){return e.listeners(t).length},o=r(1416),l=r(2861).Buffer,h=("undefined"!=typeof global?global:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){},c=Object.create(r(5622));c.inherits=r(2017);var u=r(9023),d=void 0;d=u&&u.debuglog?u.debuglog("stream"):function(){};var f,p=r(3222),m=r(5896);c.inherits(_,o);var g=["error","close","destroy","pause","resume"];function b(e,t){e=e||{};var n=t instanceof(i=i||r(5382));this.objectMode=!!e.objectMode,n&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var a=e.highWaterMark,s=e.readableHighWaterMark,o=this.objectMode?16:16384;this.highWaterMark=a||0===a?a:n&&(s||0===s)?s:o,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new p,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(f||(f=r(3141).I),this.decoder=new f(e.encoding),this.encoding=e.encoding)}function _(e){if(i=i||r(5382),!(this instanceof _))return new _(e);this._readableState=new b(e,this),this.readable=!0,e&&("function"==typeof e.read&&(this._read=e.read),"function"==typeof e.destroy&&(this._destroy=e.destroy)),o.call(this)}function y(e,t,r,n,i){var a,s=e._readableState;return null===t?(s.reading=!1,function(e,t){if(!t.ended){if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,x(e)}}(e,s)):(i||(a=function(e,t){var r,n;return n=t,l.isBuffer(n)||n instanceof h||"string"==typeof t||void 0===t||e.objectMode||(r=new TypeError("Invalid non-string/buffer chunk")),r}(s,t)),a?e.emit("error",a):s.objectMode||t&&t.length>0?("string"==typeof t||s.objectMode||Object.getPrototypeOf(t)===l.prototype||(t=function(e){return l.from(e)}(t)),n?s.endEmitted?e.emit("error",new Error("stream.unshift() after end event")):w(e,s,t,!0):s.ended?e.emit("error",new Error("stream.push() after EOF")):(s.reading=!1,s.decoder&&!r?(t=s.decoder.write(t),s.objectMode||0!==t.length?w(e,s,t,!1):C(e,s)):w(e,s,t,!1))):n||(s.reading=!1)),function(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}(s)}function w(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(e.emit("data",r),e.read(0)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&x(e)),C(e,t)}Object.defineProperty(_.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),_.prototype.destroy=m.destroy,_.prototype._undestroy=m.undestroy,_.prototype._destroy=function(e,t){this.push(null),t(e)},_.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:"string"==typeof e&&((t=t||n.defaultEncoding)!==n.encoding&&(e=l.from(e,t),t=""),r=!0),y(this,e,t,!1,r)},_.prototype.unshift=function(e){return y(this,e,null,!0,!1)},_.prototype.isPaused=function(){return!1===this._readableState.flowing},_.prototype.setEncoding=function(e){return f||(f=r(3141).I),this._readableState.decoder=new f(e),this._readableState.encoding=e,this};var v=8388608;function k(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=v?e=v:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function x(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(d("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?n.nextTick(S,e):S(e))}function S(e){d("emit readable"),e.emit("readable"),B(e)}function C(e,t){t.readingMore||(t.readingMore=!0,n.nextTick(E,e,t))}function E(e,t){for(var r=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(d("maybeReadMore read 0"),e.read(0),r!==t.length);)r=t.length;t.readingMore=!1}function A(e){d("readable nexttick read 0"),e.read(0)}function T(e,t){t.reading||(d("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),B(e),t.flowing&&!t.reading&&e.read(0)}function B(e){var t=e._readableState;for(d("flow",t.flowing);t.flowing&&null!==e.read(););}function N(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):r=function(e,t,r){var n;return e<t.head.data.length?(n=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):n=e===t.head.data.length?t.shift():r?function(e,t){var r=t.head,n=1,i=r.data;for(e-=i.length;r=r.next;){var a=r.data,s=e>a.length?a.length:e;if(s===a.length?i+=a:i+=a.slice(0,e),0==(e-=s)){s===a.length?(++n,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=a.slice(s));break}++n}return t.length-=n,i}(e,t):function(e,t){var r=l.allocUnsafe(e),n=t.head,i=1;for(n.data.copy(r),e-=n.data.length;n=n.next;){var a=n.data,s=e>a.length?a.length:e;if(a.copy(r,r.length-e,0,s),0==(e-=s)){s===a.length?(++i,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=a.slice(s));break}++i}return t.length-=i,r}(e,t),n}(e,t.buffer,t.decoder),r);var r}function R(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,n.nextTick(O,t,e))}function O(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function z(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}_.prototype.read=function(e){d("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return d("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?R(this):x(this),null;if(0===(e=k(e,t))&&t.ended)return 0===t.length&&R(this),null;var n,i=t.needReadable;return d("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&d("length less than watermark",i=!0),t.ended||t.reading?d("reading or ended",i=!1):i&&(d("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=k(r,t))),null===(n=e>0?N(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&R(this)),null!==n&&this.emit("data",n),n},_.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))},_.prototype.pipe=function(e,t){var r=this,i=this._readableState;switch(i.pipesCount){case 0:i.pipes=e;break;case 1:i.pipes=[i.pipes,e];break;default:i.pipes.push(e)}i.pipesCount+=1,d("pipe count=%d opts=%j",i.pipesCount,t);var o=t&&!1===t.end||e===process.stdout||e===process.stderr?b:l;function l(){d("onend"),e.end()}i.endEmitted?n.nextTick(o):r.once("end",o),e.on("unpipe",(function t(n,a){d("onunpipe"),n===r&&a&&!1===a.hasUnpiped&&(a.hasUnpiped=!0,d("cleanup"),e.removeListener("close",m),e.removeListener("finish",g),e.removeListener("drain",h),e.removeListener("error",p),e.removeListener("unpipe",t),r.removeListener("end",l),r.removeListener("end",b),r.removeListener("data",f),c=!0,!i.awaitDrain||e._writableState&&!e._writableState.needDrain||h())}));var h=function(e){return function(){var t=e._readableState;d("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&s(e,"data")&&(t.flowing=!0,B(e))}}(r);e.on("drain",h);var c=!1,u=!1;function f(t){d("ondata"),u=!1,!1!==e.write(t)||u||((1===i.pipesCount&&i.pipes===e||i.pipesCount>1&&-1!==z(i.pipes,e))&&!c&&(d("false write response, pause",i.awaitDrain),i.awaitDrain++,u=!0),r.pause())}function p(t){d("onerror",t),b(),e.removeListener("error",p),0===s(e,"error")&&e.emit("error",t)}function m(){e.removeListener("finish",g),b()}function g(){d("onfinish"),e.removeListener("close",m),b()}function b(){d("unpipe"),r.unpipe(e)}return r.on("data",f),function(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?a(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,"error",p),e.once("close",m),e.once("finish",g),e.emit("pipe",r),i.flowing||(d("pipe resume"),r.resume()),e},_.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r)),this;if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var a=0;a<i;a++)n[a].emit("unpipe",this,{hasUnpiped:!1});return this}var s=z(t.pipes,e);return-1===s||(t.pipes.splice(s,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r)),this},_.prototype.on=function(e,t){var r=o.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var i=this._readableState;i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.emittedReadable=!1,i.reading?i.length&&x(this):n.nextTick(A,this))}return r},_.prototype.addListener=_.prototype.on,_.prototype.resume=function(){var e=this._readableState;return e.flowing||(d("resume"),e.flowing=!0,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,n.nextTick(T,e,t))}(this,e)),this},_.prototype.pause=function(){return d("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(d("pause"),this._readableState.flowing=!1,this.emit("pause")),this},_.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;for(var i in e.on("end",(function(){if(d("wrapped end"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)})),e.on("data",(function(i){d("wrapped data"),r.decoder&&(i=r.decoder.write(i)),r.objectMode&&null==i||(r.objectMode||i&&i.length)&&(t.push(i)||(n=!0,e.pause()))})),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var a=0;a<g.length;a++)e.on(g[a],this.emit.bind(this,g[a]));return this._read=function(t){d("wrapped _read",t),n&&(n=!1,e.resume())},this},Object.defineProperty(_.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),_._fromList=N},4610:(e,t,r)=>{"use strict";e.exports=s;var n=r(5382),i=Object.create(r(5622));function a(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(!n)return this.emit("error",new Error("write callback called multiple times"));r.writechunk=null,r.writecb=null,null!=t&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function s(e){if(!(this instanceof s))return new s(e);n.call(this,e),this._transformState={afterTransform:a.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",o)}function o(){var e=this;"function"==typeof this._flush?this._flush((function(t,r){l(e,t,r)})):l(this,null,null)}function l(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(e._transformState.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}i.inherits=r(2017),i.inherits(s,n),s.prototype.push=function(e,t){return this._transformState.needTransform=!1,n.prototype.push.call(this,e,t)},s.prototype._transform=function(e,t,r){throw new Error("_transform() is not implemented")},s.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},s.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},s.prototype._destroy=function(e,t){var r=this;n.prototype._destroy.call(this,e,(function(e){t(e),r.emit("close")}))}},6708:(e,t,r)=>{"use strict";var n=r(3225);function i(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,r){var n=e.entry;for(e.entry=null;n;){var i=n.callback;t.pendingcb--,i(undefined),n=n.next}t.corkedRequestsFree.next=e}(t,e)}}e.exports=g;var a,s=!process.browser&&["v0.10","v0.9."].indexOf(process.version.slice(0,5))>-1?setImmediate:n.nextTick;g.WritableState=m;var o=Object.create(r(5622));o.inherits=r(2017);var l,h={deprecate:r(7983)},c=r(1416),u=r(2861).Buffer,d=("undefined"!=typeof global?global:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){},f=r(5896);function p(){}function m(e,t){a=a||r(5382),e=e||{};var o=t instanceof a;this.objectMode=!!e.objectMode,o&&(this.objectMode=this.objectMode||!!e.writableObjectMode);var l=e.highWaterMark,h=e.writableHighWaterMark,c=this.objectMode?16:16384;this.highWaterMark=l||0===l?l:o&&(h||0===h)?h:c,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var u=!1===e.decodeStrings;this.decodeStrings=!u,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var r=e._writableState,i=r.sync,a=r.writecb;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(r),t)!function(e,t,r,i,a){--t.pendingcb,r?(n.nextTick(a,i),n.nextTick(k,e,t),e._writableState.errorEmitted=!0,e.emit("error",i)):(a(i),e._writableState.errorEmitted=!0,e.emit("error",i),k(e,t))}(e,r,i,t,a);else{var o=w(r);o||r.corked||r.bufferProcessing||!r.bufferedRequest||y(e,r),i?s(_,e,r,o,a):_(e,r,o,a)}}(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new i(this)}function g(e){if(a=a||r(5382),!(l.call(g,this)||this instanceof a))return new g(e);this._writableState=new m(e,this),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final)),c.call(this)}function b(e,t,r,n,i,a,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,r?e._writev(i,t.onwrite):e._write(i,a,t.onwrite),t.sync=!1}function _(e,t,r,n){r||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,n(),k(e,t)}function y(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,a=new Array(n),s=t.corkedRequestsFree;s.entry=r;for(var o=0,l=!0;r;)a[o]=r,r.isBuf||(l=!1),r=r.next,o+=1;a.allBuffers=l,b(e,t,!0,t.length,a,"",s.finish),t.pendingcb++,t.lastBufferedRequest=null,s.next?(t.corkedRequestsFree=s.next,s.next=null):t.corkedRequestsFree=new i(t),t.bufferedRequestCount=0}else{for(;r;){var h=r.chunk,c=r.encoding,u=r.callback;if(b(e,t,!1,t.objectMode?1:h.length,h,c,u),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function w(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function v(e,t){e._final((function(r){t.pendingcb--,r&&e.emit("error",r),t.prefinished=!0,e.emit("prefinish"),k(e,t)}))}function k(e,t){var r=w(t);return r&&(function(e,t){t.prefinished||t.finalCalled||("function"==typeof e._final?(t.pendingcb++,t.finalCalled=!0,n.nextTick(v,e,t)):(t.prefinished=!0,e.emit("prefinish")))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"))),r}o.inherits(g,c),m.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(m.prototype,"buffer",{get:h.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(l=Function.prototype[Symbol.hasInstance],Object.defineProperty(g,Symbol.hasInstance,{value:function(e){return!!l.call(this,e)||this===g&&e&&e._writableState instanceof m}})):l=function(e){return e instanceof this},g.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},g.prototype.write=function(e,t,r){var i,a=this._writableState,s=!1,o=!a.objectMode&&(i=e,u.isBuffer(i)||i instanceof d);return o&&!u.isBuffer(e)&&(e=function(e){return u.from(e)}(e)),"function"==typeof t&&(r=t,t=null),o?t="buffer":t||(t=a.defaultEncoding),"function"!=typeof r&&(r=p),a.ended?function(e,t){var r=new Error("write after end");e.emit("error",r),n.nextTick(t,r)}(this,r):(o||function(e,t,r,i){var a=!0,s=!1;return null===r?s=new TypeError("May not write null values to stream"):"string"==typeof r||void 0===r||t.objectMode||(s=new TypeError("Invalid non-string/buffer chunk")),s&&(e.emit("error",s),n.nextTick(i,s),a=!1),a}(this,a,e,r))&&(a.pendingcb++,s=function(e,t,r,n,i,a){if(!r){var s=function(e,t,r){return e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=u.from(t,r)),t}(t,n,i);n!==s&&(r=!0,i="buffer",n=s)}var o=t.objectMode?1:n.length;t.length+=o;var l=t.length<t.highWaterMark;if(l||(t.needDrain=!0),t.writing||t.corked){var h=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:a,next:null},h?h.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else b(e,t,!1,o,n,i,a);return l}(this,a,o,e,t,r)),s},g.prototype.cork=function(){this._writableState.corked++},g.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||y(this,e))},g.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(g.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),g.prototype._write=function(e,t,r){r(new Error("_write() is not implemented"))},g.prototype._writev=null,g.prototype.end=function(e,t,r){var i=this._writableState;"function"==typeof e?(r=e,e=null,t=null):"function"==typeof t&&(r=t,t=null),null!=e&&this.write(e,t),i.corked&&(i.corked=1,this.uncork()),i.ending||function(e,t,r){t.ending=!0,k(e,t),r&&(t.finished?n.nextTick(r):e.once("finish",r)),t.ended=!0,e.writable=!1}(this,i,r)},Object.defineProperty(g.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),g.prototype.destroy=f.destroy,g.prototype._undestroy=f.undestroy,g.prototype._destroy=function(e,t){this.end(),t(e)}},3222:(e,t,r)=>{"use strict";var n=r(2861).Buffer,i=r(9023);e.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r},e.prototype.concat=function(e){if(0===this.length)return n.alloc(0);for(var t,r,i=n.allocUnsafe(e>>>0),a=this.head,s=0;a;)t=i,r=s,a.data.copy(t,r),s+=a.data.length,a=a.next;return i},e}(),i&&i.inspect&&i.inspect.custom&&(e.exports.prototype[i.inspect.custom]=function(){var e=i.inspect({length:this.length});return this.constructor.name+" "+e})},5896:(e,t,r)=>{"use strict";var n=r(3225);function i(e,t){e.emit("error",t)}e.exports={destroy:function(e,t){var r=this,a=this._readableState&&this._readableState.destroyed,s=this._writableState&&this._writableState.destroyed;return a||s?(t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,n.nextTick(i,this,e)):n.nextTick(i,this,e)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,(function(e){!t&&e?r._writableState?r._writableState.errorEmitted||(r._writableState.errorEmitted=!0,n.nextTick(i,r,e)):n.nextTick(i,r,e):t&&t(e)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},1416:(e,t,r)=>{e.exports=r(2203)},4198:(e,t,r)=>{var n=r(2203);"disable"===process.env.READABLE_STREAM&&n?(e.exports=n,(t=e.exports=n.Readable).Readable=n.Readable,t.Writable=n.Writable,t.Duplex=n.Duplex,t.Transform=n.Transform,t.PassThrough=n.PassThrough,t.Stream=n):((t=e.exports=r(5412)).Stream=n||t,t.Readable=t,t.Writable=r(6708),t.Duplex=r(5382),t.Transform=r(4610),t.PassThrough=r(3600))},2861:(e,t,r)=>{var n=r(181),i=n.Buffer;function a(e,t){for(var r in e)t[r]=e[r]}function s(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?e.exports=n:(a(n,t),t.Buffer=s),a(i,s),s.from=function(e,t,r){if("number"==typeof e)throw new TypeError("Argument must not be a number");return i(e,t,r)},s.alloc=function(e,t,r){if("number"!=typeof e)throw new TypeError("Argument must be a number");var n=i(e);return void 0!==t?"string"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},s.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i(e)},s.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return n.SlowBuffer(e)}},2791:function(){!function(e,t){"use strict";if(!e.setImmediate){var r,n,i,a,s,o=1,l={},h=!1,c=e.document,u=Object.getPrototypeOf&&Object.getPrototypeOf(e);u=u&&u.setTimeout?u:e,"[object process]"==={}.toString.call(e.process)?r=function(e){process.nextTick((function(){f(e)}))}:function(){if(e.postMessage&&!e.importScripts){var t=!0,r=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=r,t}}()?(a="setImmediate$"+Math.random()+"$",s=function(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(a)&&f(+t.data.slice(a.length))},e.addEventListener?e.addEventListener("message",s,!1):e.attachEvent("onmessage",s),r=function(t){e.postMessage(a+t,"*")}):e.MessageChannel?((i=new MessageChannel).port1.onmessage=function(e){f(e.data)},r=function(e){i.port2.postMessage(e)}):c&&"onreadystatechange"in c.createElement("script")?(n=c.documentElement,r=function(e){var t=c.createElement("script");t.onreadystatechange=function(){f(e),t.onreadystatechange=null,n.removeChild(t),t=null},n.appendChild(t)}):r=function(e){setTimeout(f,0,e)},u.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var i={callback:e,args:t};return l[o]=i,r(o),o++},u.clearImmediate=d}function d(e){delete l[e]}function f(e){if(h)setTimeout(f,0,e);else{var t=l[e];if(t){h=!0;try{!function(e){var t=e.callback,r=e.args;switch(r.length){case 0:t();break;case 1:t(r[0]);break;case 2:t(r[0],r[1]);break;case 3:t(r[0],r[1],r[2]);break;default:t.apply(undefined,r)}}(t)}finally{d(e),h=!1}}}}}("undefined"==typeof self?"undefined"==typeof global?this:global:self)},3141:(e,t,r)=>{"use strict";var n=r(2861).Buffer,i=n.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function a(e){var t;switch(this.encoding=function(e){var t=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof t&&(n.isEncoding===i||!i(e)))throw new Error("Unknown encoding: "+e);return t||e}(e),this.encoding){case"utf16le":this.text=l,this.end=h,t=4;break;case"utf8":this.fillLast=o,t=4;break;case"base64":this.text=c,this.end=u,t=3;break;default:return this.write=d,void(this.end=f)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(t)}function s(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function o(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function l(e,t){if((e.length-t)%2==0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function h(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function c(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function u(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function d(e){return e.toString(this.encoding)}function f(e){return e&&e.length?this.write(e):""}t.I=a,a.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},a.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},a.prototype.text=function(e,t){var r=function(e,t,r){var n=t.length-1;if(n<r)return 0;var i=s(t[n]);return i>=0?(i>0&&(e.lastNeed=i-1),i):--n<r||-2===i?0:(i=s(t[n]))>=0?(i>0&&(e.lastNeed=i-2),i):--n<r||-2===i?0:(i=s(t[n]))>=0?(i>0&&(2===i?i=0:e.lastNeed=i-3),i):0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)},a.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},6815:(e,t,r)=>{var n=r(4198).Transform,i=r(2017);function a(e){n.call(this,e),this._destroyed=!1}function s(e,t,r){r(null,e)}function o(e){return function(t,r,n){return"function"==typeof t&&(n=r,r=t,t={}),"function"!=typeof r&&(r=s),"function"!=typeof n&&(n=null),e(t,r,n)}}i(a,n),a.prototype.destroy=function(e){if(!this._destroyed){this._destroyed=!0;var t=this;process.nextTick((function(){e&&t.emit("error",e),t.emit("close")}))}},e.exports=o((function(e,t,r){var n=new a(e);return n._transform=t,r&&(n._flush=r),n})),e.exports.ctor=o((function(e,t,r){function n(t){if(!(this instanceof n))return new n(t);this.options=Object.assign({},e,t),a.call(this,this.options)}return i(n,a),n.prototype._transform=t,r&&(n.prototype._flush=r),n})),e.exports.obj=o((function(e,t,r){var n=new a(Object.assign({objectMode:!0,highWaterMark:16},e));return n._transform=t,r&&(n._flush=r),n}))},8129:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(6029),i=r(708);r(6815),t.transformStream=n.transformStream,t.filter=i.filter,t.getElementById=i.getElementById,t.getElementsByClassName=i.getElementsByClassName,t.parse=i.parse,t.simplify=i.simplify,t.simplifyLostLess=i.simplifyLostLess,t.stringify=i.stringify,t.toContentString=i.toContentString},6029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(708);function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var a=i(r(6815));t.transformStream=function(e,t){t||(t={}),"string"==typeof e&&(e=e.length);var r=e||0,i="";return a.default({readableObjectMode:!0},(function(e,a,s){i+=e;for(var o=0;;){if(!(r=i.indexOf("<",r)+1))return r=o,s();if("/"!==i[r])if("!"!==i[r]||"-"!==i[r+1]||"-"!==i[r+2]){var l=n.parse(i,{...t,pos:r-1,parseNode:!0,setPos:!0});if((r=l.pos)>i.length-1||r<o)return i=i.slice(o),r=0,s();this.push(l),o=r}else{const e=i.indexOf("--\x3e",r+3);if(-1===e)return i=i.slice(o),r=0,s();t.keepComments&&this.push(i.substring(r-1,e+3)),r=e+1,o=e}else o=r+=1}}))}},708:(e,t)=>{"use strict";function r(e,t){var r=(t=t||{}).pos||0,a=!!t.keepComments,s=!!t.keepWhitespace,o="<",l="<".charCodeAt(0),h=">",c=">".charCodeAt(0),u="-".charCodeAt(0),d="/".charCodeAt(0),f="!".charCodeAt(0),p="'".charCodeAt(0),m='"'.charCodeAt(0),g="[".charCodeAt(0),b="]".charCodeAt(0);function _(t){for(var n=[];e[r];)if(e.charCodeAt(r)==l){if(e.charCodeAt(r+1)===d){var i=r+2;if(r=e.indexOf(h,r),-1==e.substring(i,r).indexOf(t)){var p=e.substring(0,r).split("\n");throw new Error("Unexpected close tag\nLine: "+(p.length-1)+"\nColumn: "+(p[p.length-1].length+1)+"\nChar: "+e[r])}return r+1&&(r+=1),n}if(e.charCodeAt(r+1)===f){if(e.charCodeAt(r+2)==u){const t=r;for(;-1!==r&&(e.charCodeAt(r)!==c||e.charCodeAt(r-1)!=u||e.charCodeAt(r-2)!=u||-1==r);)r=e.indexOf(h,r+1);-1===r&&(r=e.length),a&&n.push(e.substring(t,r+1))}else{if(e.charCodeAt(r+2)===g&&e.charCodeAt(r+8)===g&&"cdata"===e.substr(r+3,5).toLowerCase()){var m=e.indexOf("]]>",r);-1==m?(n.push(e.substr(r+9)),r=e.length):(n.push(e.substring(r+9,m)),r=m+3);continue}{const t=r+1;r+=2;for(var _=!1;(e.charCodeAt(r)!==c||!0===_)&&e[r];)e.charCodeAt(r)===g?_=!0:!0===_&&e.charCodeAt(r)===b&&(_=!1),r++;n.push(e.substring(t,r))}}r++;continue}var y=k();n.push(y),"?"===y.tagName[0]&&(n.push(...y.children),y.children=[])}else{var w=(x=void 0,x=r,-2==(r=e.indexOf(o,r)-1)&&(r=e.length),e.slice(x,r+1));if(s)w.length>0&&n.push(w);else{var v=w.trim();v.length>0&&n.push(v)}r++}var x;return n}var y="\r\n\t>/= ";function w(){for(var t=r;-1===y.indexOf(e[r])&&e[r];)r++;return e.slice(t,r)}var v=t.noChildNodes||["img","br","input","meta","link","hr"];function k(){r++;const t=w(),n={};let i=[];for(;e.charCodeAt(r)!==c&&e[r];){var a=e.charCodeAt(r);if(a>64&&a<91||a>96&&a<123){for(var s=w(),o=e.charCodeAt(r);o&&o!==p&&o!==m&&!(o>64&&o<91||o>96&&o<123)&&o!==c;)r++,o=e.charCodeAt(r);if(o===p||o===m){var l=(h=void 0,u=void 0,h=e[r],u=r+1,r=e.indexOf(h,u),e.slice(u,r));if(-1===r)return{tagName:t,attributes:n,children:i}}else l=null,r--;n[s]=l}r++}var h,u;if(e.charCodeAt(r-1)!==d)if("script"==t){var f=r+1;r=e.indexOf("<\/script>",r),i=[e.slice(f,r)],r+=9}else"style"==t?(f=r+1,r=e.indexOf("</style>",r),i=[e.slice(f,r)],r+=8):-1===v.indexOf(t)?(r++,i=_(t)):r++;else r++;return{tagName:t,attributes:n,children:i}}var x,S=null;if(void 0!==t.attrValue)for(t.attrName=t.attrName||"id",S=[];-1!==(void 0,x=new RegExp("\\s"+t.attrName+"\\s*=['\"]"+t.attrValue+"['\"]").exec(e),r=x?x.index:-1);)-1!==(r=e.lastIndexOf("<",r))&&S.push(k()),e=e.substr(r),r=0;else S=t.parseNode?k():_("");return t.filter&&(S=i(S,t.filter)),t.simplify?n(Array.isArray(S)?S:[S]):(t.setPos&&(S.pos=r),S)}function n(e){var t={};if(!e.length)return"";if(1===e.length&&"string"==typeof e[0])return e[0];for(var r in e.forEach((function(e){if("object"==typeof e){t[e.tagName]||(t[e.tagName]=[]);var r=n(e.children);t[e.tagName].push(r),Object.keys(e.attributes).length&&"string"!=typeof r&&(r._attributes=e.attributes)}})),t)1==t[r].length&&(t[r]=t[r][0]);return t}function i(e,t,r=0,n=""){var a=[];return e.forEach((function(e,s){if("object"==typeof e&&t(e,s,r,n)&&a.push(e),e.children){var o=i(e.children,t,r+1,(n?n+".":"")+s+"."+e.tagName);a=a.concat(o)}})),a}Object.defineProperty(t,"__esModule",{value:!0}),t.filter=i,t.getElementById=function(e,t,n){var i=r(e,{attrValue:t});return n?tXml.simplify(i):i[0]},t.getElementsByClassName=function(e,t,n){const i=r(e,{attrName:"class",attrValue:"[a-zA-Z0-9- ]*"+t+"[a-zA-Z0-9- ]*"});return n?tXml.simplify(i):i},t.parse=r,t.simplify=n,t.simplifyLostLess=function e(t,r={}){var n={};return t.length?1===t.length&&"string"==typeof t[0]?Object.keys(r).length?{_attributes:r,value:t[0]}:t[0]:(t.forEach((function(t){if("object"==typeof t){n[t.tagName]||(n[t.tagName]=[]);var r=e(t.children||[],t.attributes);n[t.tagName].push(r),Object.keys(t.attributes).length&&(r._attributes=t.attributes)}})),n):n},t.stringify=function(e){var t="";function r(e){if(e)for(var r=0;r<e.length;r++)"string"==typeof e[r]?t+=e[r].trim():n(e[r])}function n(e){for(var n in t+="<"+e.tagName,e.attributes)null===e.attributes[n]?t+=" "+n:-1===e.attributes[n].indexOf('"')?t+=" "+n+'="'+e.attributes[n].trim()+'"':t+=" "+n+"='"+e.attributes[n].trim()+"'";"?"!==e.tagName[0]?(t+=">",r(e.children),t+="</"+e.tagName+">"):t+="?>"}return r(e),t},t.toContentString=function e(t){if(Array.isArray(t)){var r="";return t.forEach((function(t){r=(r+=" "+e(t)).trim()})),r}return"object"==typeof t?e(t.children):" "+t}},7983:(e,t,r)=>{e.exports=r(9023).deprecate},181:e=>{"use strict";e.exports=require("buffer")},4434:e=>{"use strict";e.exports=require("events")},2203:e=>{"use strict";e.exports=require("stream")},9023:e=>{"use strict";e.exports=require("util")}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var a=t[n]={exports:{}};return e[n].call(a.exports,a,a.exports,r),a.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{"use strict";function e(e){console.log(e+"my first!")}r.r(n),r.d(n,{getStructsXmlInfoByFile:()=>y,getTextByFile:()=>e});var t,i,a,s,o,l=r(8833),h=r.n(l);!function(e){e[e.CHROMIUM_UNAVAILABLE=0]="CHROMIUM_UNAVAILABLE"}(t||(t={})),t.CHROMIUM_UNAVAILABLE,function(e){e[e.DontSecret=1]="DontSecret",e[e.AllSecret=2]="AllSecret",e[e.PartSecret=3]="PartSecret"}(i||(i={})),function(e){e[e.AutoCustom=0]="AutoCustom",e[e.Date=1]="Date",e[e.DateAndHMS=2]="DateAndHMS",e[e.DateAndHM=3]="DateAndHM",e[e.HMS=4]="HMS"}(a||(a={})),function(e){e[e.Empty=0]="Empty",e[e.TextBox=1]="TextBox",e[e.Section=2]="Section",e[e.NumberBox=3]="NumberBox",e[e.DateTimeBox=4]="DateTimeBox",e[e.ListBox=5]="ListBox",e[e.MultiListBox=6]="MultiListBox",e[e.Combox=7]="Combox",e[e.MultiCombox=8]="MultiCombox",e[e.RadioButton=9]="RadioButton",e[e.CheckBox=10]="CheckBox",e[e.MultiCheckBox=11]="MultiCheckBox",e[e.Region=12]="Region",e[e.SignatureBox=13]="SignatureBox",e[e.MultiRadio=14]="MultiRadio"}(s||(s={})),function(e){e[e.Single=1]="Single",e[e.Double=2]="Double",e[e.Triple=3]="Triple"}(o||(o={}));const c={serialNumber:void 0,placeholder:"  ",helpTip:"",isMustFill:0,deleteProtect:0,editProtect:0,copyProtect:0,showBorder:1,borderString:"[",editReverse:0,backgroundColorHidden:0,customProperty:new Map,tabJump:1,newControlHidden:0,cascade:void 0,secretType:i.DontSecret,fixedLength:void 0,maxLength:void 0,title:void 0,minValue:void 0,maxValue:void 0,precision:void 0,unit:void 0,retrieve:0,selectPrefixContent:"",prefixContent:"",separator:"，",dateBoxFormat:a.DateAndHMS,customFormat:null,startDate:"无",endDate:"无",showRight:0,checked:0,printSelected:0,label:"",showType:s.RadioButton,spacenum:1,signatureCount:o.Single,preText:"  ",signatureSeparator:" ",postText:"  ",signaturePlaceholder:"",signatureRatio:1,rowHeightRestriction:1,showPlaceholder:0},u=(s.Combox,s.ListBox,s.TextBox,s.CheckBox,s.NumberBox,s.MultiListBox,s.MultiCombox,s.DateTimeBox,s.RadioButton,s.MultiCheckBox,s.Section,s.Region,s.SignatureBox,s.MultiRadio,{1:s.Combox,2:s.ListBox,3:s.TextBox,4:s.CheckBox,5:s.NumberBox,6:s.MultiListBox,7:s.MultiCombox,8:s.DateTimeBox,9:s.RadioButton,10:s.MultiCheckBox,13:s.Section,14:s.Region,15:s.SignatureBox,16:s.MultiRadio});var d;!function(e){e[e.String=1]="String",e[e.Boolean=2]="Boolean",e[e.Number=3]="Number"}(d||(d={}));const f={bCanntDelete:0,bCanntEdit:0,bHidden:0,bReverseEdit:0,title:void 0,serialNumber:void 0,bShowTitle:0,customProperty:new Map,maxLength:0,newControlInfo:void 0};var p;!function(e){e[e.Header=1]="Header",e[e.Document=2]="Document",e[e.Footer=3]="Footer"}(p||(p={}));const m={[p.Document]:1,[p.Header]:2,[p.Footer]:3};var g,b;!function(e){e[e.ReplaceStructText=1]="ReplaceStructText",e[e.ReplaceTableText=2]="ReplaceTableText",e[e.ReplaceHomePage=3]="ReplaceHomePage"}(g||(g={})),g.ReplaceStructText,g.ReplaceTableText,g.ReplaceHomePage,s.Section,function(e){e.DefaultTextColor="#788190",e.DefaultMustInputTextColor="#FF3143"}(b||(b={}));const _=r(8129);async function y(e,t){const r=new(h());if(!e)return"file param is null.";const{headerBuffer:n,contentBuffer:i}=function(e){return e?{headerBuffer:e.subarray(0,24),contentBuffer:e.subarray(24)}:null}(e),{versionNum:a}=function(e){if(null!=e){const t=e;return{versionNum:parseInt(t[5]+"",8),documentNum:parseInt(t[6]+"",8)+parseInt(t[7]+"",8),bHtmlFile:parseInt(t[8]+"",8)}}return null}(n);let s=null,o=null,l=null;await r.loadAsync(i),r.forEach(((e,t)=>{switch(e){case"Document.xml":s=t;break;case"Header1.xml":o=t;break;case"Footer.xml":l=t}}));const c=[null!=s?s.async("string"):null,null!=o?o.async("string"):null,null!=l?l.async("string"):null];try{const[e,r,n]=await Promise.all(c);let i="",s="",o="";null!=e&&(i=v(e,a,p.Document,t)),null!=r&&(s=v(r,a,p.Header,t)),null!=n&&(o=v(n,a,p.Footer,t));const l=JSON.parse(i),h=JSON.parse(s),u=JSON.parse(o),d={table:[]};if(null!=l&&null!=l.table&&(d.table=l.table),null!=h&&null!=h.table)for(const e of h.table)d.table.push(e);if(null!=u&&null!=u.table)for(const e of u.table)d.table.push(e);const f={};return Object.assign(f,l,h,u,d),console.log(JSON.stringify(f)),function(e,t){const r=w(JSON.parse(e),t);return JSON.stringify(r)}(JSON.stringify(f),a)}catch(e){throw new Error(e)}}function w(e,t){if("string"==typeof e){let t=e;return null==t||0===t.length?t:e.replace(/&apos;/g,"'").replace(/&quot;/g,'"').replace(/&gt;/g,">").replace(/&lt;/g,"<").replace(/&amp;/g,"&")}if("number"==typeof e||"boolean"==typeof e||null==e)return e;if(Array.isArray(e))return e.map((e=>w(e,t)));{const r={};return Object.keys(e).forEach((n=>{r[n]=w(e[n],t)})),r}}function v(e,t,r,n){const i={},a=_.parse(e)[1];if("object"==typeof a)switch(a.tagName){case"w:document":{const e=a.children[0];"object"==typeof e&&E(e.children,i,t,r,n);break}case"w:hdr":case"w:ftr":E(a.children,i,t,r,n)}return JSON.stringify(i)}function k(e,t,r,n,i,a){r=r||null,n=n||[];let s=!1;for(const i of e.children)if("object"==typeof i)if("sdt"===i.tagName){const e=i.attributes,o=i.children,l={content_text:"",type:3,location:1,property:{serialNumber:c.serialNumber,placeholder:c.placeholder,helpTip:c.helpTip,isMustFill:1===c.isMustFill,deleteProtect:1===c.deleteProtect,editProtect:1===c.editProtect,copyProtect:1===c.copyProtect,showBorder:1===c.showBorder,borderString:c.borderString,editReverse:1===c.editReverse,backgroundColorHidden:1===c.backgroundColorHidden,customProperty:c.customProperty,newControlHidden:1===c.newControlHidden,tabJump:1===c.tabJump,showRight:1===c.showRight,cascade:c.cascade,secretType:c.secretType,fixedLength:c.fixedLength,maxLength:c.maxLength,title:c.title,hideHasTitle:1===c.hideHasTitle,minValue:c.minValue,maxValue:c.maxValue,precision:c.precision,unit:c.unit,retrieve:1===c.retrieve,selectPrefixContent:c.selectPrefixContent,prefixContent:c.prefixContent,separator:c.separator,itemList:void 0,showValue:void 0,dateBoxFormat:c.dateBoxFormat,customFormat:c.customFormat,startDate:c.startDate,endDate:c.endDate,checked:1===c.checked,printSelected:1===c.printSelected,label:c.label,showType:c.showType,spaceNum:c.spaceNum,signatureCount:c.signatureCount,preText:c.preText,signatureSeparator:c.signatureSeparator,postText:c.postText,signaturePlaceholder:c.signaturePlaceholder,signatureRatio:c.signatureRatio,rowHeightRestriction:1===c.rowHeightRestriction,showPlaceholder:1===c.showPlaceholder}};let h="";l.type=u[+e.type],null!=a&&(l.location=m[a]);for(const e of o)if("object"==typeof e){const t=e.tagName,i=l.property;switch(t){case"w:sdtcontent":{const t=e.children;for(let e=0,a=t.length;e<a;e++){let a=t[e];if("object"==typeof a&&"w:r"===a.tagName||"w:ins"===a.tagName){if(a=x(a),null==a)continue;for(const t of a.children)if("object"==typeof t&&"w:t"===t.tagName){const a=t.children[0]||"";if((0===e||1===e)&&(a===i.title||a===i.placeholder&&!0===i.showPlaceholder))continue;if(h+=a,null!=r){const e=r.regionTexts;if(e.length>0)for(let t=0,r=e.length;t<r;t++)e[t]+=a}if(n.length>0)for(let e=0,t=n.length;e<t;e++)n[e].content_text+=a}}else if("object"==typeof a&&15===l.type&&"sdt"===a.tagName)for(const e of a.children)if("object"==typeof e&&"w:sdtcontent"===e.tagName){for(let t of e.children)if("object"==typeof t&&"w:r"===t.tagName||"w:ins"==t.tagName){if(t=x(t),null==t)continue;for(const e of t.children)if("object"==typeof e&&"w:t"===e.tagName){const t=e.children[0]||"";if(-1!==t.indexOf(i.signaturePlaceholder))continue;if(h+=t,null!=r){const e=r.regionTexts;if(e.length>0)for(let r=0,n=e.length;r<n;r++)e[r]+=t}if(n.length>0)for(let e=0,r=n.length;e<r;e++)n[e].content_text+=t}}break}}break}case"logicEvent":break;case"serialNumber":i.serialNumber=e.children[0]||"";break;case"placeholder":i.placeholder=e.children[0]||"";break;case"isMustFill":i.isMustFill="1"===e.children[0];break;case"deleteProtect":i.deleteProtect="1"===e.children[0];break;case"editProtect":i.editProtect="1"===e.children[0];break;case"copyProtect":i.copyProtect="1"===e.children[0];break;case"showBorder":i.showBorder="1"===e.children[0];break;case"borderString":i.borderString=e.children[0]||"";break;case"editReverse":i.editReverse="1"===e.children[0];break;case"backgroundColorHidden":i.backgroundColorHidden="1"===e.children[0];break;case"customProperty":{const t={};for(const r of e.children)if("object"==typeof r){const e=+r.attributes.type;if(e===d.String){const e=r.children[0]||"";t[r.tagName]=e}else e===d.Number?t[r.tagName]=+r.children[0]:e===d.Boolean&&(void 0===r.children[0]?t[r.tagName]="":t[r.tagName]="true"===r.children[0])}i.customProperty=t;break}case"tabJump":i.tabJump="1"===e.children[0];break;case"newControlHidden":i.newControlHidden="1"===e.children[0];break;case"listItems":{const t=[];for(const r of e.children)if("object"==typeof r&&"listItem"===r.tagName){const e={};for(const t of r.children)"object"==typeof t&&("name"===t.tagName?e.name=t.children[0]||"":"value"===t.tagName?e.value=t.children[0]||"":"select"===t.tagName&&(e.select=+t.children[0]));t.push(e)}i.listItems=t;break}case"helpTip":i.helpTip=e.children[0]||"";break;case"showPlaceholder":"1"===e.children[0]&&(i.showPlaceholder=!0);break;case"secretType":i.secretType=+e.children[0];break;case"fixedLength":i.fixedLength=+e.children[0];break;case"maxLength":i.maxLength=+e.children[0];break;case"title":i.title=e.children[0]||"";break;case"hideHasTitle":i.hideHasTitle="1"===e.children[0];case"minValue":i.minValue=+e.children[0];break;case"maxValue":i.maxValue=+e.children[0];break;case"precision":i.precision=+e.children[0];break;case"unit":i.unit=e.children[0]||"";break;case"retrieve":i.retrieve="1"===e.children[0];break;case"selectPrefixContent":i.selectPrefixContent=e.children[0]||"";break;case"prefixContent":i.prefixContent=e.children[0]||"";break;case"separator":i.separator=e.children[0]||"";break;case"showValue":i.showValue="1"===e.children[0];break;case"dateType":i.dateType=+e.children[0];break;case"customDateFormat":i.customDateFormat=e.children[0]||"";break;case"startDate":i.startDate=e.children[0]||"";break;case"endDate":i.endDate=e.children[0]||"";break;case"showRight":i.showRight="1"===e.children[0];break;case"checked":i.checked="1"===e.children[0];break;case"printSelected":i.printSelected="1"===e.children[0];break;case"label":i.label=e.children[0]||"";break;case"showType":i.showType=+e.children[0],!0===isNaN(i.showType)&&(i.showType=c.showType);break;case"spaceNum":i.spaceNum=+e.children[0];break;case"signatureCount":i.signatureCount=+e.children[0];break;case"preText":i.preText=e.children[0]||"";break;case"signatureSeparator":i.signatureSeparator=e.children[0]||"";break;case"postText":i.postText=e.children[0]||"";break;case"signaturePlaceholder":i.signaturePlaceholder=e.children[0]||"";break;case"signatureRatio":i.signatureRatio=+e.children[0]||"";break;case"rowHeightRestriction":i.rowHeightRestriction="1"===e.children[0]}}l.content_text=h,t[e.name]=l,s=!1}else if("sectionStart"===i.tagName){const e=i.attributes,r=i.children,o={content_text:"",type:13,location:1,property:{serialNumber:c.serialNumber,placeholder:c.placeholder,isMustFill:1===c.isMustFill,deleteProtect:1===c.deleteProtect,editProtect:1===c.editProtect,copyProtect:1===c.copyProtect,showBorder:1===c.showBorder,borderString:c.borderString,editReverse:1===c.editReverse,backgroundColorHidden:1===c.backgroundColorHidden,customProperty:c.customProperty,tabJump:1===c.tabJump,newControlHidden:1===c.newControlHidden,helpTip:c.helpTip,showPlaceholder:1===c.showPlaceholder,secretType:c.secretType,fixedLength:c.fixedLength,maxLength:c.maxLength,title:c.title}},l=+u[e.type];o.type=l,null!=a&&(o.location=m[a]);for(const e of r)if("object"==typeof e){const t=e.tagName,r=o.property;switch(t){case"serialNumber":r.serialNumber=e.children[0]||"";break;case"placeholder":r.placeholder=e.children[0]||"";break;case"isMustFill":r.isMustFill="1"===e.children[0];break;case"deleteProtect":r.deleteProtect="1"===e.children[0];break;case"editProtect":r.editProtect="1"===e.children[0];break;case"copyProtect":r.copyProtect="1"===e.children[0];break;case"showBorder":r.showBorder="1"===e.children[0];break;case"borderString":r.borderString=e.children[0];break;case"editReverse":r.editReverse="1"===e.children[0];break;case"backgroundColorHidden":r.backgroundColorHidden="1"===e.children[0];break;case"customProperty":{const t={};for(const r of e.children)if("object"==typeof r){const e=+r.attributes.type;if(e===d.String){const e=r.children[0]||"";t[r.tagName]=e}else e===d.Number?t[r.tagName]=+r.children[0]:e===d.Boolean&&(void 0===r.children[0]?t[r.tagName]="":t[r.tagName]="true"===r.children[0])}r.customProperty=t;break}case"tabJump":r.tabJump="1"===e.children[0];break;case"newControlHidden":r.newControlHidden="1"===e.children[0];break;case"helpTip":r.helpTip=e.children[0];break;case"showPlaceholder":"1"===e.children[0]&&(r.showPlaceholder=!0);break;case"secretType":r.secretType=+e.children[0];break;case"fixedLength":r.fixedLength=+e.children[0];break;case"maxLength":r.maxLength=+e.children[0];break;case"title":r.title=e.children[0]}}t[e.name]=o,n.push(o),s=!0}else if("sectionEnd"===i.tagName)n.length>0?n.splice(n.length-1,1):console.warn("no section found at sectionEnd"),s=!1;else if("w:r"===i.tagName||"w:ins"===i.tagName){let e=i;if(e=x(e),null==e)continue;let t=!1;for(const i of e.children)if("object"==typeof i&&"w:t"===i.tagName){const e=i.children[0];if("string"!=typeof e)continue;if(null!=r){const t=r.regionTexts;if(t.length>0){const{paraIndexInRegion:n,rg:i}=r;if(0===n&&null!=i&&i.property.title===e)continue;for(let r=0,n=t.length;r<n;r++)t[r]+=e}}if(n.length>0){if(!0===s){const r=n[n.length-1];if(r.property.title===e){t=!0;continue}if(-1!==e.indexOf(r.property.placeholder))continue}for(let t=0,r=n.length;t<r;t++)n[t].content_text+=e}}!1===t&&(s=!1)}}function x(e){if("object"!=typeof e)return null;if("w:ins"===e.tagName){const t=e.children[0];if("object"==typeof t&&"w:r"===t.tagName)return t}else if("w:r"===e.tagName)return e;return null}function S(e,t,r,n,i,a,s){r=r||[],n=n||[];const o={type:100,location:m[a],name:void 0,CustomProperty:{}};if(!0===s){const r=e.attributes.name;o.name=r,null==t.table&&(t.table=[]),t.table.push(o)}for(const l of e.children){if(!0===s&&"object"==typeof l&&"w:tblPr"===l.tagName)for(const e of l.children)if("object"==typeof e&&"customProperty"===e.tagName){const t=e.children;if(t.length>0){const e=A(t,i);o.CustomProperty=e}}if("object"==typeof l&&"w:tr"===l.tagName)for(const e of l.children)if("object"==typeof e&&"w:tc"===e.tagName)for(const i of e.children)if("object"==typeof i&&"w:p"===i.tagName){let e=null;r.length>0&&(e={regionTexts:r,rg:null,paraIndexInRegion:-1}),k(i,t,e,n,0,a)}}}function C(e,t,r,n,i,a){r=r||[],n=n||[];const s=e.attributes,o={content_text:"",type:14,location:1,property:{serialNumber:f.serialNumber,deleteProtect:1===f.bCanntDelete,editProtect:1===f.bCanntEdit,hidden:1===f.bHidden,editReverse:1===f.bCanntEdit,title:f.title,showTitle:1===f.bShowTitle,customProperty:f.customProperty,maxLength:f.maxLength}};r.push(""),t[s.name]=o;let l=-1;for(const s of e.children)if("object"==typeof s)if("rgPr"===s.tagName){const e=o.property;for(const t of s.children)if("object"==typeof t)switch(t.tagName){case"deleteProtect":e.deleteProtect="1"===t.children[0];break;case"editProtect":e.editProtect="1"===t.children[0];break;case"hidden":e.hidden="1"===t.children[0];break;case"editReverse":e.editReverse="1"===t.children[0];break;case"title":e.title=t.children[0];break;case"showTitle":e.showTitle="1"===t.children[0];break;case"beginPos":case"endPos":default:break;case"serialNumber":e.serialNumber=t.children[0];break;case"maxLength":e.maxLength=+t.children[0];break;case"customProperty":{const r={};for(const e of t.children)if("object"==typeof e){const t=+e.attributes.type;if(t===d.String){const t=e.children[0]||"";r[e.tagName]=t}else t===d.Number?r[e.tagName]=+e.children[0]:t===d.Boolean&&(void 0===e.children[0]?r[e.tagName]="":r[e.tagName]="true"===e.children[0])}e.customProperty=r;break}}}else"w:p"===s.tagName?(l++,k(s,t,{regionTexts:r,rg:o,paraIndexInRegion:l},n,0,p.Document)):"w:tbl"===s.tagName?S(s,t,r,n,i,p.Document,a):"rg"===s.tagName&&C(s,t,r,n,i,a);let h="";r.length>0&&(h=r.splice(r.length-1,1)[0]),o.content_text=h}function E(e,t,r,n,i){const a=[];for(const s of e)"object"==typeof s&&("w:p"===s.tagName?k(s,t,null,a,0,n):"w:tbl"===s.tagName?S(s,t,[],a,r,n,i):"rg"===s.tagName&&C(s,t,[],a,r,i))}function A(e,t){const r={};for(const n of e)if("object"==typeof n){const e=+n.attributes.type;if(e===d.String){const e=n.children[0]||"";r[n.tagName]=w(e,t)}else e===d.Number?r[n.tagName]=+n.children[0]:e===d.Boolean&&(void 0===n.children[0]?r[n.tagName]="":r[n.tagName]="true"===n.children[0])}return r}})(),n})()));