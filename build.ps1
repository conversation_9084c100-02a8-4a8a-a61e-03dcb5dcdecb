#!/usr/bin/env pwsh
# hzeditor-encryption-server-go Docker Build Script
# This script is used to build, tag and push Docker images

param (
    [string]$Version = "1.0.0",
    [string]$Registry = "",
    [string]$ImageName = "editor",
    [switch]$Push = $false,
    [switch]$UseMainDockerfile = $false
)

# Read version from version file
if (Test-Path -Path "../hzeditor-docker/version") {
    $versionFromFile = Get-Content -Path "../hzeditor-docker/version" -Raw
    $versionFromFile = $versionFromFile.Trim()
    if (-not [string]::IsNullOrEmpty($versionFromFile)) {
        $Version = $versionFromFile
    }
}

# Display build information
Write-Host "Starting build for ${ImageName} version ${Version} Docker image..." -ForegroundColor Green

# Create version info file
$versionInfo = @{
    version = $Version
    buildTime = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
} | ConvertTo-Json

Set-Content -Path "version.json" -Value $versionInfo -Encoding UTF8
Write-Host "Created version info file: version.json" -ForegroundColor Cyan

# Select Dockerfile
$dockerfilePath = "Dockerfile"
if ($UseMainDockerfile) {
    $dockerfilePath = "Dockerfile.main"
    Write-Host "Using Dockerfile.main for build" -ForegroundColor Yellow
} else {
    Write-Host "Using standard Dockerfile for build" -ForegroundColor Yellow
}

# Build Docker image
Write-Host "Building Docker image..." -ForegroundColor Cyan
docker build -t $ImageName -f $dockerfilePath .

# Check if build was successful
if ($LASTEXITCODE -ne 0) {
    Write-Host "Docker image build failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Docker image build successful: $ImageName" -ForegroundColor Green

# Tag the image
if ([string]::IsNullOrEmpty($Registry)) {
    $taggedImage = "${ImageName}:${Version}"
} else {
    $taggedImage = "${Registry}/${ImageName}:${Version}"
}
Write-Host "Tagging image: $taggedImage" -ForegroundColor Cyan
docker tag $ImageName $taggedImage

# Check if tagging was successful
if ($LASTEXITCODE -ne 0) {
    Write-Host "Docker image tagging failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Docker image tagging successful: $taggedImage" -ForegroundColor Green

# Push the image to registry if Push parameter is specified
if ($Push) {
    Write-Host "Pushing image to registry: $taggedImage" -ForegroundColor Cyan
    docker push $taggedImage
    
    # Check if push was successful
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Docker image push failed!" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Docker image push successful: $taggedImage" -ForegroundColor Green
} else {
    Write-Host "Skipping image push. Use -Push parameter to push the image" -ForegroundColor Yellow
}

# Display usage instructions
Write-Host "`nUsage Instructions:" -ForegroundColor Cyan
Write-Host "Run image: docker run --rm -ti -p 7075:7075 $taggedImage" -ForegroundColor White
Write-Host "View images: docker images" -ForegroundColor White

Write-Host "`nBuild completed!" -ForegroundColor Green
