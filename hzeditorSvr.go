package main

import (
	_ "embed"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"os/exec"
	"time"

	"github.com/julienschmidt/httprouter"
	//	"github.com/robertkrimen/otto"
)

// //go:embed bundle.js
// var javaScriptCode string

func getStructsXmlInfoByFile_Handle(writer http.ResponseWriter, request *http.Request, params httprouter.Params) {

	// 从请求参数中获取 file 参数
	err := request.ParseMultipartForm(10 << 20) // 10 MB 的最大内存
	if err != nil {
		fmt.Println("解析file出错:", err)
		http.Error(writer, "file文件过大", http.StatusInternalServerError)
		return
	}

	// 获取文件
	file, _, err := request.FormFile("file")
	if err != nil {
		fmt.Println("解析上传的文件失败:", err)
		http.Error(writer, "解析上传的文件失败", http.StatusInternalServerError)
		return
	}
	defer file.Close()

	// 读取文件内容到内存中
	fileContent, err := io.ReadAll(file)
	if err != nil {
		fmt.Println("读取文件内容失败:", err)
		http.Error(writer, "读取文件内容失败", http.StatusInternalServerError)
		return
	}

	// 将文件内容转换为 Base64 编码
	base64Content := base64.StdEncoding.EncodeToString(fileContent)
	// 创建一个缓冲通道来传递结果
	resultChan := make(chan []byte)

	// 异步执行 Node.js 命令
	go func(base64Content string) {
		// 构建 JavaScript 命令，将参数传递给 JavaScript 函数
		cmd := exec.Command("node", "-e", fmt.Sprintf(`
                const { getStructsXmlInfoByFile } = require('./bundle.js');
                const buffer = Buffer.from('%s', 'base64');
                const resultPromise = getStructsXmlInfoByFile(buffer);
                // 使用 await 关键字等待 Promise 对象的结果
                resultPromise.then(result => {
                    console.log(result); // 输出结果
                }).catch(error => {
                    console.error('执行 JavaScript 函数时发生错误:', error);
                });
            `, base64Content))

		// 捕获命令的输出结果
		output, err := cmd.CombinedOutput()
		if err != nil {
			fmt.Println("执行命令时发生错误:", err)
			resultChan <- nil
			return
		}

		// 将结果写入缓冲通道
		resultChan <- output
	}(base64Content)

	// 等待结果或超时
	select {
	case result := <-resultChan:
		if result == nil {
			http.Error(writer, "执行 Node.js 命令失败", http.StatusInternalServerError)
			return
		}
		writer.Header().Set("Content-Type", "application/xml")
		writer.Write(result)
	case <-time.After(10 * time.Second): // 设置超时时间，防止无限等待
		http.Error(writer, "执行 Node.js 命令超时", http.StatusInternalServerError)
		return
	}
}

// func start_editor_svr(r *httprouter.Router) {

// 	// 设置路由
// 	r.POST("/editorSvr/getStructsXmlInfoByFile", func(writer http.ResponseWriter, request *http.Request, params httprouter.Params) {
// 		// 从请求参数中获取 file 参数
// 		//file := request.URL.Query().Get("file")
// 		err := request.ParseMultipartForm(10 << 20) // 10 MB 的最大内存
// 		if err != nil {
// 			fmt.Println("解析file出错:", err)
// 			http.Error(writer, "file文件过大", http.StatusInternalServerError)
// 			return
// 		}

// 		// 获取文件
// 		file, _, err := request.FormFile("file")
// 		if err != nil {
// 			fmt.Println("解析上传的文件失败:", err)
// 			http.Error(writer, "解析上传的文件失败", http.StatusInternalServerError)
// 			return
// 		}
// 		defer file.Close()
// 		// 从multipart/form-data中获取文本字段
// 		//param := request.FormValue("param")

// 		// 读取文件内容并转换为 Buffer 类型的数据
// 		// 读取文件内容到内存中
// 		fileContent, err := io.ReadAll(file)
// 		if err != nil {
// 			fmt.Println("读取文件内容失败:", err)
// 			http.Error(writer, "读取文件内容失败", http.StatusInternalServerError)
// 			return
// 		}

// 		// 将文件内容转换为 Base64 编码
// 		base64Content := base64.StdEncoding.EncodeToString(fileContent)
// 		// 创建一个缓冲通道来传递结果
// 		resultChan := make(chan []byte)

// 		// 异步执行 Node.js 命令
// 		go func(base64Content string) {
// 			// 构建 JavaScript 命令，将参数传递给 JavaScript 函数
// 			cmd := exec.Command("node", "-e", fmt.Sprintf(`
//                 const { getStructsXmlInfoByFile } = require('./bundle.js');
//                 const buffer = Buffer.from('%s', 'base64');
//                 const resultPromise = getStructsXmlInfoByFile(buffer);
//                 // 使用 await 关键字等待 Promise 对象的结果
//                 resultPromise.then(result => {
//                     console.log(result); // 输出结果
//                 }).catch(error => {
//                     console.error('执行 JavaScript 函数时发生错误:', error);
//                 });
//             `, base64Content))

// 			// 捕获命令的输出结果
// 			output, err := cmd.CombinedOutput()
// 			if err != nil {
// 				fmt.Println("执行命令时发生错误:", err)
// 				resultChan <- nil
// 				return
// 			}

// 			// 将结果写入缓冲通道
// 			resultChan <- output
// 		}(base64Content)

// 		// 从缓冲通道中获取结果，并将结果返回给前端
// 		result := <-resultChan
// 		if result == nil {
// 			http.Error(writer, "执行 Node.js 命令失败", http.StatusInternalServerError)
// 			return
// 		}
// 		writer.Write(result)
// 	})
// }

/*
func getTextByApo(jsStr string, file string) string {

	// 创建一个 JavaScript 上下文
	ctx := js.Global()

	// 在 JavaScript 上下文中执行 JavaScript 代码
	err := ctx.Call("eval", jsCode)
	if err != nil {
		fmt.Println("无法执行 JavaScript 代码:", err)
		return ""
	}

	// 调用 JavaScript 函数并获取返回值
	result := callJSFunction(ctx, "getTextByApo", file)

	return result
}

// callJSFunction 是用于调用 JavaScript 函数的辅助函数
func callJSFunction(ctx js.Value, functionName string, args ...interface{}) string {
	// 获取 JavaScript 函数对象
	jsFunc := ctx.Get(functionName)

	// 调用 JavaScript 函数并获取返回值
	result := jsFunc.Invoke(args...)

	// 将 JavaScript 函数的返回值转换为 Go 语言字符串
	return result.String()
}*/
