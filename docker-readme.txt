1 新建一个dockfile
2 输入：
  FROM docker-mirror.sh.synyi.com/golang:1.18 as build
WORKDIR /code
COPY . .
RUN GOPROXY=https://goproxy.cn CGO_ENABLED=0 go build -o /server -ldflags '-w -s'
FROM scratch
WORKDIR /app
COPY --from=build /server .
ENTRYPOINT ["./server"]
3 编译 docker build -t editor .
4 运行 ：
 docker run --rm -ti -p 7075:7075 -e EDITOR_LICENCE=*********************************************************************************************************************************************************************************************************************************************************************************************************************************** editor
5 打标签 
  docker tag editor docker-insecure.sh.synyi.com/editor:1.0.0  //水印版本1.1.x 非水印（精简版）版本1.2.x //公共组件版 1.0.x
6 push
  docker push docker-insecure.sh.synyi.com/editor:1.0.0