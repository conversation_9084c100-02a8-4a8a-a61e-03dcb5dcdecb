package main

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"fmt"
	"strings"
	"time"
)

var key = []byte{0x3b, 0x7a, 0x25, 0x16, 0x38, 0x1e, 0xd4, 0xa7,
	0xab, 0xf8, 0x15, 0x78, 0x09, 0xcf, 0x5f, 0x4c}

//backup key
//var key = []byte{0x95, 0x7a, 0x25, 0x16, 0x38, 0x1e, 0xd4, 0xa7,
//	0xab, 0xf8, 0x15, 0x78, 0x09, 0xcf, 0x5f, 0x27}

var iv = []byte{0x02, 0x01, 0x02, 0x03, 0x14, 0x25, 0x34, 0x37,
	0x08, 0x59, 0x01, 0x1b, 0x0c, 0x23, 0x09, 0x7f}

//backup iv
//var iv = []byte{0x85, 0x01, 0x02, 0x03, 0x14, 0x25, 0x34, 0x37,
//	0x08, 0x59, 0x01, 0x1b, 0x0c, 0x23, 0x09, 0x17}

func decRequest(payload []byte) (string, error) {
	payloadDecoded := make([]byte, base64.RawStdEncoding.DecodedLen(len(payload)))
	base64.StdEncoding.Decode(payloadDecoded, payload)

	b, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}
	dec := cipher.NewCBCDecrypter(b, iv)
	out := make([]byte, len(payloadDecoded))
	dec.CryptBlocks(out, payloadDecoded)
	code := string(out)
	segments := strings.Split(code, ",")
	// ts:=segments[0]
	// nCode:=segments[2]
	uuid := segments[3]
	return strings.TrimRight(uuid, "\x00"), nil
}

func encResponse(nResult int, uuid string, displayName string) []byte {
	ts := time.Now().UnixMilli()
	payload := fmt.Sprintf("%d,%d,%d,%s,%s", ts, nResult, (ts-1)%8, uuid, displayName)

	b, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}
	enc := cipher.NewCBCEncrypter(b, iv)
	payloadData := pkcs5padding([]byte(payload), 16)
	out := make([]byte, len(payloadData))
	enc.CryptBlocks(out, payloadData)
	ret := make([]byte, base64.StdEncoding.EncodedLen(len(out)))
	base64.StdEncoding.Encode(ret, out)
	return ret
}

func pkcs5padding(data []byte, blocksize int) []byte {
	padding := blocksize - len(data)%blocksize
	if padding == blocksize {
		padding = 0
	}
	padtext := bytes.Repeat([]byte{byte(0)}, padding)
	return append(data, padtext...)
}
