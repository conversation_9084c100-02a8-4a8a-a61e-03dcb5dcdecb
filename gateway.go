package main

import "github.com/julienschmidt/httprouter"

func start_gateway(r *httprouter.Router) {
	// 设置路由
	r.POST("/editorSvr/v1/getStructsXmlInfoByFile", getStructsXmlInfoByFile_Handle)
	r.POST("/editorSvr/v1/convertToHtml", convertToHtml_Handle)
	r.POST("/editorSvr/v1/printToPdf", printToPdf_Handle)
	//演示系统后端
	r.POST("/demo/insertBusinessFile", insertDynamicHandler)
	r.GET("/demo/getFileContent", getFileContent_Demo_Handle) //通过id 抽取recorder表的content
	r.GET("/demo/getDatas", getDataHandle)                    //获取指定表的指定列数据
	r.POST("/demo/getBusinessFileContent", getBusinessFileContentHandler)
	r.POST("/demo/executeSQL", executeSQLHandle) //指定特定sql，返回值
	// 处理预检请求
	r.OPTIONS("/demo/insertBusinessFile", optionsHandler)
	r.OPTIONS("/demo/getFileContent", optionsHandler)
	r.OPTIONS("/demo/getDatas", optionsHandler)
	r.OPTIONS("/demo/getBusinessFileContent", optionsHandler)
	r.OPTIONS("/demo/executeSQL", optionsHandler) // 处理 OPTIONS 请求
}
