# 使用多阶段构建
FROM golang:1.24 AS build

# 设置 Go 代理
ENV GOPROXY=https://goproxy.cn,direct

# 首先复制和安装依赖
WORKDIR /code
COPY go.mod go.sum ./
RUN go mod download

# 然后复制源码并构建
COPY . .
RUN CGO_ENABLED=1 GOOS=linux go build -o /server -ldflags '-w -s'

# 运行阶段
FROM debian:bookworm-slim

# 创建并配置 sources.list 为国内镜像
RUN echo "deb http://mirrors.aliyun.com/debian bookworm main contrib non-free" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian bookworm-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian-security bookworm-security main contrib non-free" >> /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
        chromium \
        nodejs \
        libsqlite3-0 && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 复制字体和构建产物
WORKDIR /app
COPY --from=build /code/fonts /usr/share/fonts
COPY --from=build /server /app/server
COPY hzeditor.db /app/hzeditor.db
COPY bundle.js /app/bundle.js
COPY version.json /app/version.json

# 设置环境变量
ENV CHROME_BIN=/usr/bin/chromium

# 启动容器
ENTRYPOINT ["/app/server"]