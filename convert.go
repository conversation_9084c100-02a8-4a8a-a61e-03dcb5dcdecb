package main

import (
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/chromedp/cdproto/page"
	"github.com/chromedp/chromedp"
	"github.com/julienschmidt/httprouter"
)

const (
	tabCount = 10
	baseURL  = "http://localhost:7075/test.html"
	poolSize = 10
)

var pool chan context.Context

func convertToHtml_Handle(writer http.ResponseWriter, request *http.Request, ps httprouter.Params) {

	// 设置跨域允许的域，*代表允许任何域
	writer.Header().Set("Access-Control-Allow-Origin", "*")

	// 从请求参数中获取 file 参数
	err := request.ParseMultipartForm(10 << 20) // 10 MB 的最大内存
	if err != nil {
		fmt.Println("解析file出错:", err)
		http.Error(writer, "file文件过大", http.StatusInternalServerError)
		return
	}

	// 获取文件
	file, _, err := request.FormFile("file")
	if err != nil {
		fmt.Println("解析上传的文件失败:", err)
		http.Error(writer, "解析上传的文件失败", http.StatusInternalServerError)
		return
	}
	defer file.Close()

	cleanModeStr := request.FormValue("cleanMode")
	needWaterMarkStr := request.FormValue("needWaterMark")

	// 将字符串转换为整数
	needWaterMark, err := strconv.Atoi(needWaterMarkStr)
	if err != nil {
		// 处理转换错误
		http.Error(writer, "Invalid needWaterMark value", http.StatusBadRequest)
		return
	}
	cleanMode, err := strconv.Atoi(cleanModeStr)
	if err != nil {
		// 处理转换错误
		http.Error(writer, "Invalid cleanModeStr value", http.StatusBadRequest)
		return
	}

	// 读取文件内容到内存中
	fileContent, err := io.ReadAll(file)
	if err != nil {
		fmt.Println("读取文件内容失败:", err)
		http.Error(writer, "读取文件内容失败", http.StatusInternalServerError)
		return
	}

	// 将文件内容转换为 Base64 编码
	base64Content := base64.StdEncoding.EncodeToString(fileContent)

	htmlcontent := convertHtml(base64Content, cleanMode, needWaterMark)

	// 将内容作为响应返回
	// 设置响应头
	writer.Header().Set("Content-Type", "text/html")
	writer.Header().Set("Content-Disposition", "attachment; filename=editor.html") // 可选，如果要求浏览器下载

	fmt.Fprint(writer, htmlcontent)

}

func initChromedpPool() {
	pool = make(chan context.Context, poolSize)

	// 创建初始的 Chromedp 实例并放入缓冲池
	for i := 0; i < poolSize; i++ {
		ctx, cancel := chromedp.NewContext(context.Background())
		pool <- ctx
		go func() {
			<-ctx.Done()
			cancel()
		}()
	}
}

func closeChromedpPool() {
	close(pool)
}

func convertHtml(base64String string, cleanMode int, needWaterMark int) string {
	ctx := <-pool
	defer func() {
		pool <- ctx // 将资源放回缓冲池
	}()

	// 导航到指定网页
	if err := chromedp.Run(ctx, chromedp.Navigate(baseURL)); err != nil {
		log.Fatalf("导航到网页失败: %v", err)
	}

	// 等待网页加载完毕
	var frameLoaded bool
	if err := chromedp.Run(ctx, chromedp.WaitVisible("#hz-editor-app > iframe"), chromedp.Evaluate(`document.querySelector("#hz-editor-app > iframe").contentWindow.__EmrEditorComponent__ !== null`, &frameLoaded)); err != nil {
		fmt.Println("等待网页加载失败:", err)
		return ""
	}
	if !frameLoaded {
		log.Fatal("网页加载失败")
	}

	// 定义 JavaScript 代码字符串，传入参数作为函数参数
	script := `(async (param1, param2, param3) => {
        const arrayBuffer = Uint8Array.from(atob(param1), c => c.charCodeAt(0)).buffer;
        const result = await document.querySelector("#hz-editor-app > iframe").contentWindow.__EmrEditorComponent__.convertToHTMLFromBuffer(arrayBuffer, param2, param3);
        window.__resultStr__ = result;
    })("%s", %d, %d);`

	// 格式化 JavaScript 代码字符串，替换参数值
	formattedScript := fmt.Sprintf(script, base64String, cleanMode, needWaterMark)

	// 执行 JavaScript 代码并等待结果
	if err := chromedp.Run(ctx, chromedp.ActionFunc(func(ctxt context.Context) error {
		// 执行 JavaScript 代码
		if err := chromedp.EvaluateAsDevTools(formattedScript, nil).Do(ctxt); err != nil {
			return fmt.Errorf("调用网页函数失败: %v", err)
		}

		// 等待结果被设置
		return chromedp.WaitVisible(`#hz-editor-app > iframe`).Do(ctxt)
	})); err != nil {
		fmt.Println("获取结果失败:", err)
		return ""
	}

	// 添加轮询等待，确保异步操作完成并设置了结果
	var resultReady bool
	var resultStr string
	
	// 最多等待10秒
	timeout := time.After(10 * time.Second)
	tick := time.Tick(100 * time.Millisecond)

	for !resultReady {
		select {
		case <-timeout:
			fmt.Println("等待结果超时")
			return ""
		case <-tick:
			// 检查结果是否已经准备好
			err := chromedp.Run(ctx, chromedp.Evaluate(`typeof window.__resultStr__ !== 'undefined'`, &resultReady))
			if err != nil {
				fmt.Println("检查结果状态失败:", err)
				return ""
			}
		}
	}

	// 获取结果
	if err := chromedp.Run(ctx, chromedp.Evaluate(`window.__resultStr__`, &resultStr)); err != nil {
		fmt.Println("获取结果失败:", err)
		return ""
	}

	return resultStr
}

// 转换pdf
func printToPdf_Handle(writer http.ResponseWriter, request *http.Request, ps httprouter.Params) {

	// 设置跨域允许的域，*代表允许任何域
	writer.Header().Set("Access-Control-Allow-Origin", "*")

	ctx := <-pool
	defer func() {
		pool <- ctx // 将资源放回缓冲池
	}()

	// 从请求参数中获取 file 参数
	err := request.ParseMultipartForm(10 << 20) // 10 MB 的最大内存
	if err != nil {
		fmt.Println("解析file出错:", err)
		http.Error(writer, "file文件过大", http.StatusInternalServerError)
		return
	}

	// 获取文件
	file, _, err := request.FormFile("file")
	if err != nil {
		fmt.Println("convert pdf error,解析上传的html文件失败:", err)
		http.Error(writer, "解析上传的文件失败", http.StatusInternalServerError)
		return
	}
	defer file.Close()

	// 读取文件内容到内存中
	fileContent, err := io.ReadAll(file)
	if err != nil {
		fmt.Println("读取文件内容失败:", err)
		http.Error(writer, "读取文件内容失败", http.StatusInternalServerError)
		return
	}

	// 将文件内容转换为 Base64 编码
	base64Content := base64.StdEncoding.EncodeToString(fileContent)

	// 打开网页
	err = chromedp.Run(ctx,
		chromedp.Navigate("data:text/html;base64,"+string(base64Content)),
	)
	if err != nil {
		log.Fatal(err)
	}

	// 等待网页加载完成
	err = chromedp.Run(ctx, chromedp.WaitReady("body"))
	if err != nil {
		log.Fatal(err)
	}

	// 保存PDF
	var pdfData []byte
	err = chromedp.Run(ctx, chromedp.ActionFunc(func(ctx context.Context) error {
		var innerErr error
		pdfData, _, innerErr = page.PrintToPDF().Do(ctx)
		return innerErr
	}))
	if err != nil {
		log.Fatal(err)
	}

	// 设置响应头
	writer.Header().Set("Content-Type", "application/pdf")
	writer.Header().Set("Content-Disposition", "attachment; filename=editor.pdf") // 可选，如果要求浏览器下载

	// 将PDF数据写入响应主体
	_, err = writer.Write(pdfData)
	if err != nil {
		// 处理错误
		http.Error(writer, "Failed to write PDF response", http.StatusInternalServerError)
		return
	}

	log.Println("PDF created successfully")
}
