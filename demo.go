package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	// 导入SQLite驱动
	"github.com/julienschmidt/httprouter"
	_ "github.com/mattn/go-sqlite3" // 导入SQLite驱动
)

var db *sql.DB

func init_Database() bool {
	if db == nil {
		var err error
		db, err = sql.Open("sqlite3", "./hzeditor.db")
		if err != nil {
			log.Println("Error opening database:", err)
			return false
		}
	}

	// 确保数据库连接可用
	err := db.Ping()
	if err != nil {
		log.Println("Error pinging database:", err)
		return false
	}
	return true
}

func close_Database() {
	if db != nil {
		db.Close()
	}
}

// 通用的数据结构
type RowData map[string]interface{}

// getDataHandle 处理函数，根据传入的表名和列名获取数据
func getDataHandle(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
	// 设置跨域允许的域，*代表允许任何域
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 允许的方法
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	// 允许的头部字段
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	// 从请求中获取表名和列名
	table := r.URL.Query().Get("table")
	columns := r.URL.Query().Get("columns")

	// 验证表名和列名
	if !isValidTableName(table) || !isValidColumns(columns) {
		http.Error(w, "Invalid table or columns", http.StatusBadRequest)
		return
	}

	// 构造查询 SQL
	query := fmt.Sprintf("SELECT %s FROM %s", columns, table)

	// 执行查询
	rows, err := db.Query(query)
	if err != nil {
		http.Error(w, "Database query error", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	// 获取列的名称
	colNames, err := rows.Columns()
	if err != nil {
		http.Error(w, "Error fetching column names", http.StatusInternalServerError)
		return
	}

	// 用于保存查询结果的 slice
	var results []RowData

	for rows.Next() {
		// 动态创建一个接口数组来接收每一行的列数据
		columnsData := make([]interface{}, len(colNames))
		columnPointers := make([]interface{}, len(colNames))

		for i := range columnsData {
			columnPointers[i] = &columnsData[i]
		}

		// 扫描每一行数据
		err := rows.Scan(columnPointers...)
		if err != nil {
			http.Error(w, "Error scanning row", http.StatusInternalServerError)
			return
		}

		// 创建 RowData 对象，用于存储列名和值
		rowData := make(RowData)
		for i, colName := range colNames {
			rowData[colName] = columnsData[i]
		}

		results = append(results, rowData)
	}

	// 返回 JSON 格式的数据
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(results)
}

// isValidTableName 验证传入的表名是否有效，防止 SQL 注入
func isValidTableName(table string) bool {
	// 根据你允许的表名进行验证
	validTables := []string{"recorder", "elements", "other_table"} // 允许的表名列表
	for _, t := range validTables {
		if t == table {
			return true
		}
	}
	return false
}

// isValidColumns 验证传入的列名是否有效，防止 SQL 注入
func isValidColumns(columns string) bool {
	// 仅允许字母、数字和逗号
	for _, col := range strings.Split(columns, ",") {
		col = strings.TrimSpace(col)
		if col == "" || strings.ContainsAny(col, ";'\"") {
			return false
		}
	}
	return true
}

func getFileContent_Demo_Handle(writer http.ResponseWriter, request *http.Request, ps httprouter.Params) {

	// 设置跨域允许的域，*代表允许任何域
	writer.Header().Set("Access-Control-Allow-Origin", "*")
	// 允许的方法
	writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	// 允许的头部字段
	writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
	// 解析查询参数中的id
	id := request.URL.Query().Get("id")
	if id == "" {
		http.Error(writer, "Missing 'id' parameter", http.StatusBadRequest)
		return
	} else {
		log.Println("id is received", id)
	}
	// 查询数据库
	var content string
	err := db.QueryRow("SELECT content FROM recorder WHERE id=?", id).Scan(&content)
	if err != nil {
		if err == sql.ErrNoRows {
			http.Error(writer, "No content found for the given id", http.StatusNotFound)
			return
		}
		log.Printf("Error fetching content: %v", err)
		http.Error(writer, "Internal server error", http.StatusInternalServerError)
		return
	}

	// 将内容作为响应返回
	fmt.Fprint(writer, content)
}

// 写入函数，自动生成 id 并插入数据
func InsertBusinessFile(content string, name string) error {

	// 插入数据，id 由 SQLite 自动生成
	stmt, err := db.Prepare("INSERT INTO businessFile(content, name) VALUES(?, ?)")
	if err != nil {
		return fmt.Errorf("error preparing insert statement: %v", err)
	}
	defer stmt.Close()

	_, err = stmt.Exec(content, name)
	if err != nil {
		return fmt.Errorf("error inserting data into businessFile: %v", err)
	}

	log.Println("Inserted record successfully")
	return nil
}

// 写入函数，自动生成 id 并插入数据
func InsertElements(content string, name string, owner string) error {
	// 插入数据，id 由 SQLite 自动生成
	stmt, err := db.Prepare("INSERT INTO elements(content, name, owner) VALUES(?, ?, ?)")
	if err != nil {
		return fmt.Errorf("error preparing insert statement: %v", err)
	}
	defer stmt.Close()

	_, err = stmt.Exec(content, name, owner)
	if err != nil {
		return fmt.Errorf("error inserting data into elements: %v", err)
	}

	log.Println("Inserted record into elements successfully")
	return nil
}

// 根据 name 获取 content
func GetBusinessFileContentByName(tableName, name string, fields []string) (map[string]interface{}, error) {

	query := fmt.Sprintf("SELECT %s FROM %s WHERE name = ?", strings.Join(fields, ", "), tableName)
	stmt, err := db.Prepare(query)
	if err != nil {
		return nil, fmt.Errorf("error preparing query: %v", err)
	}
	defer stmt.Close()

	row := stmt.QueryRow(name)

	// 创建一个映射以存储查询结果
	result := make(map[string]interface{})
	dest := make([]interface{}, len(fields))
	for i := range fields {
		dest[i] = new(interface{})
	}

	// 执行查询
	err = row.Scan(dest...)
	if err != nil {
		return nil, fmt.Errorf("error scanning row: %v", err)
	}

	// 将结果存入映射
	for i, field := range fields {
		result[field] = *(dest[i].(*interface{}))
	}

	return result, nil
}

func optionsHandler(w http.ResponseWriter, r *http.Request, ps httprouter.Params) {
	// 添加 CORS 头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
	w.WriteHeader(http.StatusOK) // 返回 200 OK
}

// 插入数据的 HTTP 处理函数
func insertDynamicHandler(w http.ResponseWriter, r *http.Request, ps httprouter.Params) {

	// 设置跨域允许的域，*代表允许任何域
	w.Header().Set("Access-Control-Allow-Origin", "*")
	// 允许的方法
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	// 允许的头部字段
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method != http.MethodPost {
		http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求体
	var data struct {
		TableName string          `json:"tableName"`
		Fields    json.RawMessage `json:"fields"` // 动态字段
	}

	err := json.NewDecoder(r.Body).Decode(&data)
	if err != nil || data.TableName == "" {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// 根据表名插入数据
	switch data.TableName {
	case "businessFile":
		var fields struct {
			Content string `json:"content"`
			Name    string `json:"name"`
		}
		if err := json.Unmarshal(data.Fields, &fields); err != nil {
			http.Error(w, "Invalid fields data", http.StatusBadRequest)
			return
		}
		err = InsertBusinessFile(fields.Content, fields.Name)
	case "elements":
		var fields struct {
			Content string `json:"content"`
			Name    string `json:"name"`
			Owner   string `json:"owner"`
		}
		if err := json.Unmarshal(data.Fields, &fields); err != nil {
			http.Error(w, "Invalid fields data", http.StatusBadRequest)
			return
		}
		err = InsertElements(fields.Content, fields.Name, fields.Owner)
	default:
		http.Error(w, "Unknown table name", http.StatusBadRequest)
		return
	}

	if err != nil {
		http.Error(w, "Error inserting data", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
	w.Write([]byte("Record inserted successfully"))
}

// SQLResult 封装查询结果的结构
type SQLResult struct {
	Columns []string        `json:"columns"`
	Rows    [][]interface{} `json:"rows"`
}

// ExecuteSQL 执行 SQL 查询并返回结果
func ExecuteSQL(query string) (*SQLResult, error) {
	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	values := make([]interface{}, len(columns))
	for i := range values {
		values[i] = new(interface{})
	}

	var result SQLResult
	result.Columns = columns

	for rows.Next() {
		if err := rows.Scan(values...); err != nil {
			return nil, err
		}
		row := make([]interface{}, len(columns))
		for i, value := range values {
			row[i] = *(value.(*interface{}))
		}
		result.Rows = append(result.Rows, row)
	}

	return &result, nil
}

// 指定特定sql的处理函数
func executeSQLHandle(w http.ResponseWriter, r *http.Request, ps httprouter.Params) {
	// 设置跨域允许的域
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	// 处理 OPTIONS 请求
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusNoContent) // 204 No Content
		return
	}

	if r.Method != http.MethodPost {
		http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
		return
	}

	var requestBody struct {
		SQL string `json:"sql"`
	}

	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&requestBody); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	query := requestBody.SQL
	if query == "" {
		http.Error(w, "Missing SQL parameter", http.StatusBadRequest)
		return
	}

	result, err := ExecuteSQL(query)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(result); err != nil {
		http.Error(w, "Failed to encode response", http.StatusInternalServerError)
	}
}

// 根据表名和 name 获取指定字段的 HTTP 处理函数
func getBusinessFileContentHandler(w http.ResponseWriter, r *http.Request, ps httprouter.Params) {
	if r.Method != http.MethodGet {
		http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
		return
	}

	// 获取查询参数中的表名和 name
	tableName := r.URL.Query().Get("tableName")
	name := r.URL.Query().Get("name")
	if tableName == "" || name == "" {
		http.Error(w, "Missing 'tableName' or 'name' parameter", http.StatusBadRequest)
		return
	}

	// 获取需要返回的字段
	fieldsParam := r.URL.Query().Get("fields")
	var fields []string
	if fieldsParam != "" {
		if err := json.Unmarshal([]byte(fieldsParam), &fields); err != nil {
			http.Error(w, "Invalid 'fields' parameter", http.StatusBadRequest)
			return
		}
	}

	// 调用获取函数
	result, err := GetBusinessFileContentByName(tableName, name, fields)
	if err != nil {
		http.Error(w, err.Error(), http.StatusNotFound)
		return
	}

	// 返回 JSON 格式的结果
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(result); err != nil {
		http.Error(w, "Error encoding response", http.StatusInternalServerError)
		return
	}
}
