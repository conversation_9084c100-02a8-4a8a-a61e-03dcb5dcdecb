<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>鸿至编辑器演示网页</title>
  <script src="./sdk.js"></script>
  <style>
    body {
      display: flex;
      height: 100vh;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    .left-container {
      flex: 3;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border: 1px solid #ddd;
      padding: 20px;
    }

    .right-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: normal;
      padding: 10px;
      /*flex: 1;  
          background-color: #e5e5e5;
          padding: 10px;
          align-items:normal;*/
    }

    .section {
      margin-bottom: 70px;
    }

    .static-text {
      background-color: #ddd;
      padding: 5px 10px;
      margin-bottom: 5px;
    }

    .button-group {
      display: flex;
      flex-wrap: wrap;
    }

    .button {
      /* padding: 5px 10px;
      background-color: #333;
      color: #fff;
      border: none;
      cursor: pointer;*/
      margin-right: 5px;
      margin-bottom: 5px;
      padding: 5px 10px;
      background-color: #333;
      color: #fff;
      border: none;
      cursor: pointer;
    }

    .file-upload-container {
            position: relative;
            display: inline-block;
        }

        .custom-file-button {
            display: inline-block;
            padding: 8px 8px;
            font-size: 10px;
            color: #fff;
            background-color: #333;
            cursor: pointer;
            text-align: center;
        }

        .custom-file-button:hover {
            background-color: #0056b3;
            border-color: #004085;
        }

        .file-upload-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }
  </style>
</head>

<body>

  <div class="left-container" id="editor"></div>
  <div class="right-container">
    <!-- 第一组 -->
    <div class="section">
      <div class="static-text">演示病历</div>
      <div class="button-group">
        <button class="button" onclick="openFile('入院')">入院记录</button>
        <button class="button" onclick="openFile('出院')">出院记录</button>
        <button class="button" onclick="openFile('转出')">转出记录记录</button>
        <button class="button" onclick="openFile('病案首页')">病案首页</button>
        <button class="button" onclick="openFile('自理评估单')">评估量表</button>
      </div>
    </div>
    <!-- 第二组 -->
    <div class="section">
      <div class="static-text">编辑器功能演示</div>
      <div class="button-group">
        <button class="button" onclick="switchCleanMode()">清洁模式切换</button>
        <button class="button" onclick="exportToHtml()">导出html</button>
        <button class="button" onclick="exportToPdf()">导出pdf</button>
        <input type="file" id="fileInput" accept=".apo,.hz">
        <button class="button" onclick="exportToPdfbackgroud()">导出pdf(模拟后端)</button>

        <button class="button" onclick="disableOuterCopy()">禁止外部拷贝</button>
        <button class="button" onclick="CopyWithoutFormat()">拷贝不带格式</button>
        <button class="button" onclick="openFile('入院')">结构化元素</button>
        <button class="button" onclick="openFile('绘图')">绘图</button>
        <button class="button" onclick="openFile('级联')">级联</button>
        <button class="button" onclick="openFile('跌倒评估单')">计算级联</button>
        <button class="button" onclick="openFile('医学表达式')">医学表达式</button>
      </div>
    </div>
    <!-- 第三组 -->
    <div class="section">
      <div class="static-text">编辑器业务演示</div>
      <div class="button-group">
        <button class="button">病历合并</button>
        <button class="button">打印</button>
        <button class="button">浏览模式切换</button>
        <button class="button">修订</button>
        <button class="button" onclick="insertMedicalformula()">医学表达式</button>
        <div class="file-upload-container">
          <label class="custom-file-button">插入图片</label>
          <input type="file" id="imageInput" class="file-upload-input">
        </div>
        <button class="button" onclick="saveElementInfo()">读取元素</button>
        <button class="button" onclick="createElementInfo()">生成元素</button>
        <button class="button" onclick="saveRegionInfo()">读取区域</button>
        <button class="button" onclick="createRegionInfo()">生成区域素</button>
      </div>
    </div>
  </div>
  <script>
    var Editor = EmrEditorSDK.Editor;
    const dom = document.getElementById('div-editor');
	  var gElementInfo = null;
    var editorFunction = null;
    // 获取当前网页的协议、主机和端口
    const currentUrl = new URL(window.location.href);
    const baseUrl = `${currentUrl.protocol}//${currentUrl.host}/`;
    /*
    编辑器初始化
    */
    async function init(options) {
      const vm = await new EmrEditorSDK.Editor().init(options);
      // 接口集合
      const editor = await vm.getEditor();
      editorFunction = editor;
      editor.setMenuBarVisible(true);
      // 设置事件
      vm.setEvent({
        nsoStructClick: (name, type, position) => {
          console.log(name, type, position);
        }
      });
      return vm.getEditor();
    }

    //调用编辑器初始化,获取到编辑器的实例对象
    editorFunction = init({
      dom: window.document.querySelector('div'),
      src: baseUrl,
      option: {
        bShowMenu: true,
        bShowToolbar: true,
        isTest: false,
        theme: {
          NewControl: {
            ShowRegionOperator: false,
          }
        }
      }
    });

    /*
    功能函数 -- 打开文档
    */
    function openFile(name) {
      getfilecontentFromServer(getIntValueFromString(name))
        .then(blob => {
          // 在这里处理blob对象 
          //const blob2 = new Blob([blob], { type: 'application/apollo-zstd' });
          // 这里你可以进一步处理这个Blob对象，比如编辑器直接打开  
          editorFunction.openDocumentWithStream(blob);
        })
        .catch(error => {
          console.error('Error fetching blob:', error);
        });
    }

    /*
    辅助函数
    从数据库中读取文件
    */
    function getfilecontentFromServer(id) {
      // 构造请求的URL  
      //const url = `http://localhost:${port}/demo/getFileContent?id=${id}`;
      const url = `${baseUrl}demo/getFileContent?id=${id}`;

      // 使用fetch API发送GET请求  
      return fetch(url)
        .then(response => {
          // 确保响应成功  
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          // 读取响应内容  
          return response.text();
        })
        .then(base64Data => {
          // 移除base64编码的前缀（如果有的话）  
          const base64Prefix = 'data:application/octet-stream;base64,';
          let rawData = base64Data;
          if (base64Data.startsWith(base64Prefix)) {
            rawData = base64Data.substring(base64Prefix.length);
          }

          // 将base64字符串转换为Uint8Array  
          const byteCharacters = atob(rawData);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);

          // 创建Blob对象  
          const blob = new Blob([byteArray], { type: 'application/apollo-zstd' });
          return blob;

        })
        .catch(error => {
          console.error('There has been a problem with your fetch operation:', error);
        });
    }

    /*
    辅助函数，浏览下下载
    */
    function downloadBlobAsFile(filename, blobPromise) {
      blobPromise.then((blob) => {
        // 创建一个临时的a标签  
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.setAttribute('download', filename); // 设置下载文件名  
        document.body.appendChild(link);
        link.click(); // 模拟点击  
        document.body.removeChild(link); // 然后移除  
        URL.revokeObjectURL(link.href); // 释放URL对象  
      }).catch((error) => {
        console.error('下载文件失败:', error);
      });
    }

    /*
    功能函数 -- 将当前编辑器打开的病历转换成html
    */
    function exportToHtml() {

      editorFunction.exportToOtherFormatWithStream(2).then(blob => {
        // 在这里处理返回的 Blob 数据 创建一个下载链接并模拟点击下载
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = 'file.html';
        link.click();
      }).catch(error => {
        // 如果出现错误，可以在这里处理错误
        console.error("convert html error:", error);
      });
    }

    /*
    功能函数 -- 将当前编辑器打开的文档转换成pdf
    通过2个步骤完成。
    1）调用接口转换为html
    2） 请求后台服务，将html转换为pdf
    */
    function exportToPdf() {
      editorFunction.exportToOtherFormatWithStream(2).then(blob => {
        // 在这里处理返回的 Blob 数据
        // 构造FormData对象，将HTML数据放入其中
        const formData = new FormData();
        formData.append('file', blob);

        const url = `${baseUrl}editorSvr/v1/printToPdf`;
        // 发送POST请求到后台服务
        fetch(url, {
          method: 'POST',
          body: formData
        })
          .then(response => response.blob()) // 将返回的数据转换为Blob
          .then(blobData => {
            // 创建一个Blob URL
            const blobUrl = URL.createObjectURL(blobData);

            // 创建一个<a>元素
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = 'converted_pdf.pdf'; // 设置下载文件的文件名

            // 模拟点击下载
            link.click();

            // 释放Blob URL
            URL.revokeObjectURL(blobUrl);
          })
          .catch(error => {
            console.error('Error in pdf-convert:', error);
          });
      }).catch(error => {
        // 如果出现错误，可以在这里处理错误
        console.error("convert pdf error:", error);
      });

    }

    /*
    功能函数 -- 模拟后台服务-转换pdf, 只需要hz文件的原始数据即可完成转换
    */
    function exportToPdfbackgroud() {
      //读取input框的文件
      var fileInput = document.getElementById('fileInput');

      // 确保有选择文件
      if (fileInput.files.length === 0) {
        alert('Please select a file.');
        return;
      }

      // 获取第一个文件
      var file = fileInput.files[0];

      // 创建一个 FileReader 对象
      var reader = new FileReader();

      // 当文件读取完成时
      reader.onload = function (event) {
        // 获取文件内容
        var fileContent = event.target.result;

        // 构造FormData对象，将HTML转服务需要的数据放入其中
        const formData = new FormData();
        formData.append('needWaterMark', 0);
        formData.append('cleanMode', 1);

        var blob = new Blob([fileContent], { type: 'application/apollo-zstd' });
        formData.append('file',blob);

        // 调用上述两个函数，并处理返回的 PDF 数据
        requestConvertToHtml(formData)
         .then(htmlData => requestPrintToPdf(htmlData))
          .then(pdfBlob => {

           // 可以创建一个下载链接并模拟点击下载
           const blobUrl = URL.createObjectURL(pdfBlob);
           const link = document.createElement('a');
           link.href = blobUrl;
           link.download = 'converted_pdf.pdf';
           link.click();
         })
         .catch(error => {
           // 如果出现错误，可以在这里处理错误
           console.error("Error occurred:", error);
         });
      }
      reader.readAsArrayBuffer(file);


    }

    /*
    辅助函数
    用于请求 html转换服务并获取返回的数据
    */
    function requestConvertToHtml(formData) {
      const url = `${baseUrl}editorSvr/v1/convertToHtml`;
      return fetch(url, {
        method: 'POST',
        body: formData
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to fetch HTML data');
          }
          return response.text(); // 返回HTML数据的文本内容
        });
    }

    /*
    辅助函数
    用于请求 pdf转换服务
    */
    // 定义一个函数，
    function requestPrintToPdf(htmlData) {
      // 构造请求参数
      const formData = new FormData();
      var blob = new Blob([htmlData], { type: 'text/html' });
      formData.append('file', blob);

      // 发送 POST 请求到 printToPdf 服务
      const url = `${baseUrl}editorSvr/v1/printToPdf`;
      return fetch(url, {
        method: 'POST',
        body: formData
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Failed to print to PDF');
          }
          return response.blob(); // 返回 PDF 数据的 Blob
        });
    }

    /*
    功能函数 -- 切换清洁模式
    */
    function switchCleanMode() {
      editorFunction.browseTemplet(1);
    }

    /*
    功能函数 -- 禁止外部拷贝
    */
    function disableOuterCopy() {
      editorFunction.enableCopyFromExternal(false);//禁止外部拷贝
    }

    /*
    功能函数 -- 禁止外部拷贝 控制拷贝格式
    */
    function CopyWithoutFormat() {
      editorFunction.enableCopyFromExternal(true);//允许外部拷贝
      editorFunction.setClipboardFormat(false, false);//外部内部拷贝都不带格式，第二个控制内部
    }
	/*
  功能函数 -- 保存当前的区域信息
  */
 async function saveRegionInfo(){
  	//把光标放在一个区域内，开始抽取这个结构化元素的相关内容
    const sName = await editorFunction.getCurrentRegionName();
    if(sName.length ===0 ) return;

    const sProp = await editorFunction.getRegionPropByArray(sName);//抽取所有属性 包括自定义属性 此返回为一个json值

    let propObject = {};
    try {
      propObject = JSON.parse(sProp);
    } catch (error) {
      console.error('Error parsing sProp:', error);
    }

    // 将结果转换为 JSON 字符串
    gElementInfo = JSON.stringify(propObject); // 格式化输出
 }

 /*
 功能函数 -- 创建区域
 */
 async function createRegionInfo() {
    // 假设这是 数据库抽取的JSON 字符串
    const jsonString = gElementInfo; // 从数据库读取json值

    // 解析 JSON 字符串为 JavaScript 对象
    let jsonObject;
    try {
        jsonObject = JSON.parse(jsonString);
    } catch (error) {
        console.error('Error parsing JSON:', error);
        return; // 解析错误时直接返回
    }

    // 从 json 中抽取值进行元素还原
    if (jsonObject) {
      // 获取主键 也就是元素的name
      const oldname = Object.keys(jsonObject)[0];
      // 提取主键对应的对象
      const item = jsonObject[oldname];

      // 提取属性值
      const contentText = item.content_text;
      const typeValue = item.type;
      // 生成新的name
      const randomName = generateRandomString();

      let result = await editorFunction.insertRegionAtCurrentCursor(randomName);
      if (result.length === 0) return; // 插入位置非法导致

      let mainKey = Object.keys(jsonObject)[0];
      // 获取 property 对象并保留
      let resultJson = { property: jsonObject[mainKey].property };
      // 对区域进行属性还原
      let nResult = await editorFunction.setRegionPropByArray(randomName,JSON.stringify(resultJson));
      await editorFunction.setRegionText(randomName,contentText);
    }
 }
	/*
    功能函数 -- 保存当前元素信息
    */
    async function  saveElementInfo(){
	//把光标放在一个结构化元素内后，开始抽取这个结构化元素的相关内容
	const sName = await editorFunction.getCurrentStructName(); //抽取name
  if(sName.length ===0 ) return;
	const sProp = await editorFunction.getStructPropByArray(sName);//抽取所有属性 包括自定义属性 此返回为一个json值

	let propObject = {};
	try {
		propObject = JSON.parse(sProp);
	} catch (error) {
		console.error('Error parsing sProp:', error);
	}

	// 将结果转换为 JSON 字符串
	gElementInfo = JSON.stringify(propObject); // 格式化输出
	}
	
	/*
	根据电子病历数据集，刷新HDS编码
	*/
	async function updateCustomProperty(firstJsonString) {
    try {
        // 读取第二个 JSON 文件
        const response = await fetch('./emr.json');
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const secondJsonObject = await response.json();

        // 解析第一个 JSON 字符串
        const firstJsonObject = JSON.parse(firstJsonString);

        // Extract SerialNumber from Ajson
        const mainKey = Object.keys(firstJsonObject)[0];
        const serialNumber = firstJsonObject[mainKey].property.SerialNumber;

        // Find the matching item in erm.json
        const matchingItem = secondJsonObject.find(item => item.name === serialNumber);

        if (matchingItem) {
        if (!firstJsonObject[mainKey].property.CustomProperty) {
          firstJsonObject[mainKey].property.CustomProperty = {};
        }
        firstJsonObject[mainKey].property.CustomProperty.HDSCode = matchingItem.code;
        } else {
            // If no match found, create a new "HDS" property
            if (!firstJsonObject[mainKey].property.CustomProperty) {
              firstJsonObject[mainKey].property.CustomProperty = {};
            }
            firstJsonObject[mainKey].property.CustomProperty.HDSCode = "";//业务上选择需不需要设置空值
        }


        // 将更新后的对象转换回 JSON 字符串
        const updatedFirstJsonString = JSON.stringify(firstJsonObject);
        console.log("update json is",updatedFirstJsonString);
        return updatedFirstJsonString;
		} catch (error) {
			console.error('Error processing JSON:', error);
		}
	}
	
	// 随机生成8位字符串的函数
	function generateRandomString(length = 8) {
		const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		let result = '';
		for (let i = 0; i < length; i++) {
			const randomIndex = Math.floor(Math.random() * characters.length);
			result += characters[randomIndex];
		}
		return result;
	}
	
	/* 创建元素的函数 不包含签名元素*/
  async function createElementInfo() {
    // 假设这是 数据库抽取的JSON 字符串
    const jsonString = gElementInfo; // 从数据库读取json值

    // 解析 JSON 字符串为 JavaScript 对象
    let jsonObject;
    try {
        jsonObject = JSON.parse(jsonString);
    } catch (error) {
        console.error('Error parsing JSON:', error);
        return; // 解析错误时直接返回
    }

    // 从 json 中抽取值进行元素还原
    if (jsonObject) {
       // 获取主键 也就是元素的name
      const oldname = Object.keys(jsonObject)[0];
      // 提取主键对应的对象
      const item = jsonObject[oldname];

      // 提取属性值
      const contentText = item.content_text;
      const typeValue = item.type;
      // 生成新的元素 name
      const randomName = generateRandomString();

      // 还原结构化对象以及内容,签名控件需要单独处理
      if(typeValue === 15 ){
        await createSignElementFromInfo(oldname,randomName,jsonObject);
        return;
      }
      let result = await editorFunction.insertStructAtCurrentCursor(randomName, contentText, typeValue);
      if (result !== 0) return; // 插入位置非法导致

      // 替换主键
      jsonObject[randomName] = jsonObject[oldname]; // 将旧主键的内容赋值给新主键
      delete jsonObject[oldname]; // 删除旧主键
      

      console.log('Updated sProp:', JSON.stringify(jsonObject));

      // 根据电子病历数据集，刷新 HDS 编码
      let sNewProp =  await updateCustomProperty(JSON.stringify(jsonObject));
      if( sNewProp != null ){
          // 对结构化元素进行属性还原
          let nResult = await editorFunction.setStructsPropByArray(sNewProp);
      } 
      else {
        // 对结构化元素进行属性还原
          let nResult = await editorFunction.setStructsPropByArray(JSON.stringify(jsonObject));
      } 
    }
}

function transformSignatureBox(jsonInput) {

  // 获取主键
  let mainKey = Object.keys(jsonInput)[0];

  // 获取 property 对象
  let property = jsonInput[mainKey].property;

  //判断 SignNumber 与 alwaysShow的关系
  if(property.SignNumber < property.alwaysShow)
    property.SignNumber = property.alwaysShow;

  // 转换逻辑
  const result = {
    SignNumber: property.SignNumber,                         
    FrontChar: property.FrontChar,                                 
    MidChar: property.MidChar ?               // 去掉 & 符号
      property.MidChar.replace(/&/g, '') : '',
    EndChar: property.EndChar || "",                            
    SignPlaceHolder: property.SignPlaceHolder,               
    type: property.signType,                                     
    alwaysShow: property.alwaysShow || 1,
    SignRatio:1,                       
    showSignBorder: property.showSignBorder ? 1 : 0  //转换下类型
  };
  
  // 将结果转换为 JSON 字符串返回
  return JSON.stringify(result);
}

async function createSignElementFromInfo(oldname,randomName,jsonObject){
    /* 插入签名需要的json
    {
    "SignNumber":2, //签名元素个数
    "FrontChar":"", //前字符
    "MidChar":"", //中字符
    "EndChar":"",//尾字符
    "SignPlaceHolder":"",//签名子元素占位符
    "type":1,           //1:常规类型   2:集合类型
    "alwaysShow":2,    //仅当type=2 时生效
    "showSignBorder":1, //是否显示子签名元素的边框
    "SignRatio":1
    }
    */

    let result = await editorFunction.insertSignControlAtCurrentCursor(randomName, transformSignatureBox(jsonObject));
    if (typeof result !== 'string') return; // 插入位置非法导致

    // 替换主键
    jsonObject[randomName] = jsonObject[oldname]; // 将旧主键的内容赋值给新主键
    delete jsonObject[oldname]; // 删除旧主键
    

    console.log('Updated sProp:', JSON.stringify(jsonObject));

    // 根据电子病历数据集，刷新 HDS 编码
    let sNewProp =  await updateCustomProperty(JSON.stringify(jsonObject));
    if( sNewProp != null ){
        // 对结构化元素进行属性还原
        let nResult = await editorFunction.setStructsPropByArray(sNewProp);
    } 
    else {
      // 对结构化元素进行属性还原
        let nResult = await editorFunction.setStructsPropByArray(JSON.stringify(jsonObject));
    } 
}

/**
 * 将本地图片文件转换为 Base64 编码的 Data URL。
 * @param {File} file - 要转换的图片文件。
 * @param {function} callback - 转换完成后的回调函数，接受 Base64 编码的 Data URL 作为参数。
 */
 function convertImageToBase64(file, callback) {
    if (!file || !file.type.startsWith('image/')) {
        console.error('请选择一个图片文件。');
        return;
    }

    const reader = new FileReader();

    reader.onload = function(e) {
        const base64String = e.target.result;
        if (callback && typeof callback === 'function') {
            callback(base64String);
        }
    };

    reader.onerror = function(error) {
        console.error('文件读取失败：', error);
    };

    reader.readAsDataURL(file);
}

/* 
当前光标位置插入图片
*/
document.getElementById('imageInput').addEventListener('change', function(event) {
    const file = event.target.files[0];
    convertImageToBase64(file, function(base64String) {
        editorFunction.addImageWithString(base64String);
    });
});

/*
医学表达式演示
*/
async function insertMedicalformula(){
  var json1 = {
    "value1":1,
    "value2":2,
    "value3":3,
    "value4":4,
    "value5":5,
    "value6":6,
    "value7":7,
    "value8":8,
    "value9":9
  };
  var json2 = {
    "value1":9,
    "value2":8,
    "value3":7,
    "value4":6,
    "value5":5,
    "value6":4,
    "value7":3,
    "value8":2,
    "value9":1
  };
  //插入医学表达式
  await editorFunction.createNew();
  for (let nIndex = 0; nIndex <= 13; nIndex++) {
    if (nIndex === 7) {
          continue; // 跳过 nIndex 为 7 
    }
    await editorFunction.insertMedicalformula(nIndex, "randomname" + nIndex, JSON.stringify(json1));
    await editorFunction.jumpToFileEnd();
    await editorFunction.insertNewLine();
  }
  //对医学表达式进行赋值
  for (let nIndex = 0; nIndex <= 13; nIndex++) {
    if (nIndex === 7) {
          continue; // 跳过 nIndex 为 7 
    }
    await editorFunction.setMedicalformulaText("randomname" + nIndex, JSON.stringify(json2));
  }
}

    const stringToIntMap = {
      '入院': 1,
      '出院': 2,
      '医学表达式': 3,
      '病案首页': 4,
      '级联': 5,
      '绘图': 6,
      '自理评估单': 7,
      '跌倒评估单': 8,
      '转出': 9
    };

    // 字符串对照函数  
    function getIntValueFromString(str) {
      // 检查对照表中是否存在该字符串  
      if (stringToIntMap.hasOwnProperty(str)) {
        // 返回对应的整数值  
        return stringToIntMap[str];
      } else {
        // 如果不存在，返回默认值（比如 -1）  
        return -1;
      }
    }  
  </script>

</body>

</html>