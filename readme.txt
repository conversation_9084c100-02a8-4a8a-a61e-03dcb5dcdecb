1 
vs code 里配置windows 开发环境

2 windows 编译
go build -o hzEditorServerV1.exe -ldflags '-w -s'
编译出主程序

3 修改licence.json 白名单 授权信息
  生成授权文件
  ./hzEditorServerV1.exe --genlicence

  4 该授权文件为一串string
    程序启动的时候添加 
    ./hzEditorServerV1.exe --licence=xxxx

5 linux 编译
  增加 set GOARCH=amd64
GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o .\hzEditorServerV1 -ldflags '-w -s'

//增加对CGO支持
GOOS=linux GOARCH=amd64 CGO_ENABLED=1 go build -o ./hzEditorServerV1 -ldflags '-w -s'

6 docker 编译
1) 编译 ：docker build -t editor-service:v250602 .(冒号后为版本tag)
2）加载image：docker load <'mnt/d/editor_2.zip"
3) 保存image为本地文件：docker save -o D:/image_temp/editor_V250122.tar hzeditor:v250122
4) 运行:
docker run -d --restart=always -p 7075:7075 --name editor editor-service:v250602 --licence="*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

editor:v3 不带标签为最新，第一个端口为对外的端口

5) 停止docker docker stop 容器id
6) 删除容器 docker rm -f editor  查看容器 docker ps
7）删除image docker rmi -f editor 查看镜像 docker images